import os
import time
import requests
import json
import pickle
import base64
import http.client
import mimetypes
from codecs import encode
from flask import Flask, request, render_template, jsonify, send_file
from werkzeug.utils import secure_filename
import uuid
from datetime import datetime
import traceback
import re
import cv2
import subprocess
import shutil
from pathlib import Path
import numpy as np
try:
    import torch
    import torch.nn.functional as F
    TORCH_AVAILABLE = True
    print("✅ PyTorch可用，将使用高级插帧算法")
except ImportError:
    TORCH_AVAILABLE = False
    print("⚠️ PyTorch不可用，将使用基础插帧算法")
from config import NGROK_BASE_URL, VIDEO_SEAMLESS_CONFIG, ULTIMATE_SEAMLESS_CONFIG

# 导入配置
try:
    from config import API_KEY, APP_CONFIG, VIDEO_CONFIG, API_ENDPOINTS
except ImportError:
    print("❌ 错误: 找不到 config.py 文件")
    print("请确保 config.py 文件存在并配置了正确的API Key")
    exit(1)

app = Flask(__name__, static_folder='static', static_url_path='/static')
app.config['UPLOAD_FOLDER'] = APP_CONFIG['UPLOAD_FOLDER']
app.config['OUTPUT_FOLDER'] = APP_CONFIG['OUTPUT_FOLDER']
app.config['PRODUCT_FOLDER'] = 'cp'
app.config['MAX_CONTENT_LENGTH'] = APP_CONFIG['MAX_CONTENT_LENGTH']

# 确保上传和输出目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['OUTPUT_FOLDER'], exist_ok=True)

# 产品列表（可自动扫描cp目录）
PRODUCTS = [
    {"name": "Blend Oasis", "filename": "Blend Oasis（橙色）.png"},
    {"name": "Detox Oasis", "filename": "Detox Oasis（绿色）.png"},
    {"name": "Vital Oasis", "filename": "Vital Oasis（蓝色）.png"}
]

# 任务状态存储
tasks = {}

# 任务持久化文件路径
TASKS_FILE = 'tasks_data.pkl'

def save_tasks():
    """保存任务到文件"""
    try:
        with open(TASKS_FILE, 'wb') as f:
            pickle.dump(tasks, f)
    except Exception as e:
        print(f"❌ 保存任务失败: {e}")

def load_tasks():
    """从文件加载任务"""
    global tasks
    try:
        if os.path.exists(TASKS_FILE):
            with open(TASKS_FILE, 'rb') as f:
                tasks = pickle.load(f)
            print(f"✅ 加载了 {len(tasks)} 个任务")
        else:
            tasks = {}
    except Exception as e:
        print(f"❌ 加载任务失败: {e}")
        tasks = {}

def download_video_file(file_id, task_id):
    """下载视频文件到本地"""
    try:
        output_filename = f"{task_id}.mp4"
        video_path = fetch_video_result(file_id, output_filename)
        return video_path
    except Exception as e:
        print(f"❌ 下载视频文件失败: {e}")
        return None

# RunningHub API配置
RUNNINGHUB_API_KEY = "8683e9e6f4714993b429e8a1d722d0b9"
RUNNINGHUB_WORKFLOW_ID = "1942531246742507522"

# MiniMax视频API配置
VIDEO_API_KEY = "请在config.py中配置"
VIDEO_MODEL = "MiniMax-Hailuo-02"
VIDEO_DURATION = 6
VIDEO_RESOLUTION = "768P"
FIXED_VIDEO_PROMPT = "人物生动的讲解他的手中的产品，（产品里面是空的；产品未开封；人物除了看产品或看镜头不要看其他地方；产品展示过程尽量只展示产品的正面；人物必须开口讲解）"

# 配置信息
ALLOWED_EXTENSIONS = APP_CONFIG['ALLOWED_EXTENSIONS']

# DeepSeek配置（优先用config.py，否则用默认值）
try:
    import config
    DEEPSEEK_API_KEY = getattr(config, 'DEEPSEEK_API_KEY', '***********************************')
    DEEPSEEK_API_URL = getattr(config, 'DEEPSEEK_API_URL', 'https://api.deepseek.com/v1/chat/completions')
except ImportError:
    DEEPSEEK_API_KEY = '***********************************'
    DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions'

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def invoke_video_generation(image_path=None, prompt="基于上传的图片生成视频", duration=VIDEO_DURATION):
    """提交视频生成任务 - 支持纯文本提示词或图片+提示词"""
    print("-----------------提交视频生成任务-----------------")
    url = "https://api.minimaxi.com/v1/video_generation"
    
    # 构建请求参数
    payload = {
        "model": VIDEO_CONFIG['model'],
        "prompt": prompt,
        "duration": duration,
        "resolution": VIDEO_CONFIG['resolution']
    }
    
    # 如果提供了图片路径，则添加first_frame_image参数
    if image_path and os.path.exists(image_path):
        with open(image_path, "rb") as image_file:
            data = base64.b64encode(image_file.read()).decode('utf-8')
        payload["first_frame_image"] = f"data:image/jpeg;base64,{data}"
    
    headers = {
        'authorization': f'Bearer {API_KEY}',
        'Content-Type': 'application/json'
    }

    response = requests.post(url, headers=headers, json=payload)
    print(f"API响应: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        task_id = result['task_id']
        print(f"视频生成任务提交成功，任务ID: {task_id}")
        return task_id
    else:
        print(f"任务提交失败: {response.text}")
        return None

def query_video_generation(task_id):
    """查询视频生成状态"""
    url = f"https://api.minimaxi.com/v1/query/video_generation?task_id={task_id}"
    headers = {
        'authorization': f'Bearer {API_KEY}',
        'content-type': 'application/json'
    }
    
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        status = result['status']
        
        if status == 'Preparing':
            return "", 'Preparing'
        elif status == 'Queueing':
            return "", 'Queueing'
        elif status == 'Processing':
            return "", 'Processing'
        elif status == 'Success':
            return result['file_id'], "Finished"
        elif status == 'Fail':
            return "", "Fail"
        else:
            return "", "Unknown"
    else:
        print(f"查询失败: {response.text}")
        return "", "Error"

def fetch_video_result(file_id, output_filename):
    """根据file_id自动下载视频到outputs/目录"""
    print(f"⬇️  开始下载视频，file_id: {file_id}")
    url = f"https://api.minimaxi.com/v1/files/retrieve?file_id={file_id}"
    headers = {
        'authorization': f'Bearer {API_KEY}'
    }
    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            result = response.json()
            download_url = result['file']['download_url']
            print(f"下载链接: {download_url}")
            # 下载视频内容
            video_response = requests.get(download_url)
            output_path = os.path.join(app.config['OUTPUT_FOLDER'], output_filename)
            with open(output_path, 'wb') as f:
                f.write(video_response.content)
            print(f"✅ 视频已保存到: {output_path}")
            return output_path
        else:
            print(f"❌ 获取下载链接失败: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 下载异常: {e}")
        return None

def extract_video_thumbnail(video_path, output_dir, frame_time="00:00:01"):
    """
    提取视频缩略图（封面）

    Args:
        video_path (str): 视频文件路径
        output_dir (str): 输出目录
        frame_time (str): 提取帧的时间点，默认第1秒

    Returns:
        str: 缩略图路径，失败返回None
    """
    try:
        if not os.path.exists(video_path):
            print(f"❌ 视频文件不存在: {video_path}")
            return None

        # 生成输出文件名
        video_filename = os.path.basename(video_path)
        video_id = video_filename.replace('.mp4', '')
        output_filename = f'{video_id}_thumbnail.jpg'
        output_path = os.path.join(output_dir, output_filename)

        # 如果缩略图已存在，直接返回
        if os.path.exists(output_path):
            return output_path

        # 使用ffmpeg提取指定时间点的帧作为缩略图
        cmd = [
            'ffmpeg', '-y',
            '-i', video_path,
            '-ss', frame_time,
            '-frames:v', '1',
            '-q:v', '2',
            '-s', '320x180',  # 缩略图尺寸
            output_path
        ]

        print(f"🖼️ 生成视频缩略图: {video_filename}")
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0 and os.path.exists(output_path):
            print(f"✅ 缩略图生成成功: {output_filename}")
            return output_path
        else:
            print(f"❌ 缩略图生成失败: {result.stderr}")
            return None

    except Exception as e:
        print(f"❌ 生成缩略图异常: {e}")
        return None

def get_video_info(video_path):
    """
    获取视频详细信息

    Args:
        video_path (str): 视频文件路径

    Returns:
        dict: 视频信息字典
    """
    try:
        # 使用ffprobe获取视频信息
        cmd = [
            'ffprobe', '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            '-show_streams',
            video_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            import json
            data = json.loads(result.stdout)

            # 提取视频流信息
            video_stream = None
            audio_stream = None

            for stream in data.get('streams', []):
                if stream.get('codec_type') == 'video' and video_stream is None:
                    video_stream = stream
                elif stream.get('codec_type') == 'audio' and audio_stream is None:
                    audio_stream = stream

            format_info = data.get('format', {})

            # 计算视频时长
            duration = float(format_info.get('duration', 0))

            # 获取视频尺寸和帧率
            width = int(video_stream.get('width', 0)) if video_stream else 0
            height = int(video_stream.get('height', 0)) if video_stream else 0

            # 计算帧率
            fps_str = video_stream.get('r_frame_rate', '0/1') if video_stream else '0/1'
            if '/' in fps_str:
                num, den = fps_str.split('/')
                fps = float(num) / float(den) if float(den) != 0 else 0
            else:
                fps = float(fps_str)

            # 计算总帧数
            total_frames = int(duration * fps) if fps > 0 else 0

            return {
                'duration': duration,
                'width': width,
                'height': height,
                'fps': round(fps, 2),
                'total_frames': total_frames,
                'file_size': int(format_info.get('size', 0)),
                'bitrate': int(format_info.get('bit_rate', 0)),
                'video_codec': video_stream.get('codec_name', 'unknown') if video_stream else 'unknown',
                'audio_codec': audio_stream.get('codec_name', 'unknown') if audio_stream else 'unknown'
            }
        else:
            print(f"❌ 获取视频信息失败: {result.stderr}")
            return None

    except Exception as e:
        print(f"❌ 获取视频信息异常: {e}")
        return None

def extract_last_frame(video_path, output_dir=None):
    """
    提取视频的最后一帧并保存为图片

    Args:
        video_path (str): 视频文件路径
        output_dir (str): 输出目录，默认为视频同目录

    Returns:
        str: 提取的尾帧图片路径，失败返回None
    """
    try:
        if not os.path.exists(video_path):
            print(f"❌ 视频文件不存在: {video_path}")
            return None

        # 打开视频文件
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"❌ 无法打开视频文件: {video_path}")
            return None

        # 获取视频总帧数
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames <= 0:
            print(f"❌ 视频帧数无效: {total_frames}")
            cap.release()
            return None

        # 跳转到最后一帧
        cap.set(cv2.CAP_PROP_POS_FRAMES, total_frames - 1)

        # 读取最后一帧
        ret, frame = cap.read()
        cap.release()

        if not ret or frame is None:
            print(f"❌ 无法读取视频最后一帧")
            return None

        # 确定输出路径
        if output_dir is None:
            output_dir = os.path.dirname(video_path)

        # 生成输出文件名
        video_name = os.path.splitext(os.path.basename(video_path))[0]
        output_filename = f"{video_name}_last_frame.jpg"
        output_path = os.path.join(output_dir, output_filename)

        # 保存最后一帧
        success = cv2.imwrite(output_path, frame)
        if success:
            print(f"✅ 视频尾帧已保存到: {output_path}")
            return output_path
        else:
            print(f"❌ 保存尾帧失败: {output_path}")
            return None

    except Exception as e:
        print(f"❌ 提取视频尾帧异常: {e}")
        return None

# ========== FFmpeg视频处理工具函数 ==========

def get_video_frame_count(video_path):
    """
    获取视频的总帧数

    Args:
        video_path (str): 视频文件路径

    Returns:
        int: 视频总帧数，失败返回0
    """
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-count_frames', '-select_streams', 'v:0',
            '-show_entries', 'stream=nb_frames', '-of', 'csv=p=0', video_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        frame_count = int(result.stdout.strip())
        print(f"📊 视频 {os.path.basename(video_path)} 总帧数: {frame_count}")
        return frame_count
    except Exception as e:
        print(f"❌ 获取视频帧数失败: {e}")
        return 0

def extract_frame_sequence(video_path, output_dir, start_frame=None, frame_count=None):
    """
    从视频中提取帧序列

    Args:
        video_path (str): 视频文件路径
        output_dir (str): 输出目录
        start_frame (int): 起始帧号（可选）
        frame_count (int): 提取帧数（可选）

    Returns:
        bool: 是否成功
    """
    try:
        os.makedirs(output_dir, exist_ok=True)

        # 构建FFmpeg命令
        cmd = ['ffmpeg', '-i', video_path]

        # 添加帧选择滤镜
        if start_frame is not None and frame_count is not None:
            # 提取指定范围的帧
            end_frame = start_frame + frame_count - 1
            vf = f"select='gte(n,{start_frame})*lte(n,{end_frame})'"
        elif start_frame is not None:
            # 从指定帧开始提取
            vf = f"select='gte(n,{start_frame})'"
        elif frame_count is not None:
            # 提取前N帧
            vf = f"select='lt(n,{frame_count})'"
        else:
            # 提取所有帧
            vf = None

        if vf:
            cmd.extend(['-vf', vf])

        cmd.extend(['-vsync', 'vfr', '-q:v', '2', os.path.join(output_dir, 'img%04d.png')])

        print(f"🎬 提取帧序列: {' '.join(cmd)}")
        subprocess.run(cmd, check=True, capture_output=True)

        # 检查输出文件
        output_files = list(Path(output_dir).glob('img*.png'))
        print(f"✅ 成功提取 {len(output_files)} 帧到 {output_dir}")
        return True

    except Exception as e:
        print(f"❌ 提取帧序列失败: {e}")
        return False

def encode_frames_to_video(frames_dir, output_video, fps=60, crf=23, preset='medium'):
    """
    将帧序列编码为视频

    Args:
        frames_dir (str): 帧序列目录
        output_video (str): 输出视频路径
        fps (int): 帧率
        crf (int): 视频质量参数
        preset (str): 编码预设

    Returns:
        bool: 是否成功
    """
    try:
        cmd = [
            'ffmpeg', '-framerate', str(fps), '-i', os.path.join(frames_dir, 'img%04d.png'),
            '-c:v', 'libx264', '-pix_fmt', 'yuv420p', '-crf', str(crf),
            '-preset', preset, '-r', str(fps), '-y', output_video
        ]

        print(f"🎬 编码视频: {' '.join(cmd)}")
        subprocess.run(cmd, check=True, capture_output=True)

        if os.path.exists(output_video):
            print(f"✅ 视频编码成功: {output_video}")
            return True
        else:
            print(f"❌ 视频编码失败: 输出文件不存在")
            return False

    except Exception as e:
        print(f"❌ 视频编码失败: {e}")
        return False

def trim_video(input_video, output_video, start_frame=None, end_frame=None):
    """
    裁剪视频

    Args:
        input_video (str): 输入视频路径
        output_video (str): 输出视频路径
        start_frame (int): 起始帧（可选）
        end_frame (int): 结束帧（可选）

    Returns:
        bool: 是否成功
    """
    try:
        cmd = ['ffmpeg', '-i', input_video]

        # 构建trim滤镜
        if start_frame is not None and end_frame is not None:
            vf = f"select='gte(n,{start_frame})*lte(n,{end_frame})'"
        elif start_frame is not None:
            vf = f"select='gte(n,{start_frame})'"
        elif end_frame is not None:
            vf = f"select='lte(n,{end_frame})'"
        else:
            print("❌ 裁剪参数错误: 需要指定起始帧或结束帧")
            return False

        cmd.extend(['-vf', vf, '-vsync', 'vfr', '-c:v', 'libx264', '-y', output_video])

        print(f"✂️ 裁剪视频: {' '.join(cmd)}")
        subprocess.run(cmd, check=True, capture_output=True)

        if os.path.exists(output_video):
            print(f"✅ 视频裁剪成功: {output_video}")
            return True
        else:
            print(f"❌ 视频裁剪失败: 输出文件不存在")
            return False

    except Exception as e:
        print(f"❌ 视频裁剪失败: {e}")
        return False

def concat_videos(video_list, output_video):
    """
    拼接多个视频

    Args:
        video_list (list): 视频文件路径列表
        output_video (str): 输出视频路径

    Returns:
        bool: 是否成功
    """
    try:
        # 创建拼接列表文件
        concat_list_path = 'temp_concat_list.txt'
        with open(concat_list_path, 'w') as f:
            for video_path in video_list:
                f.write(f"file '{os.path.abspath(video_path)}'\n")

        cmd = [
            'ffmpeg', '-f', 'concat', '-safe', '0', '-i', concat_list_path,
            '-c', 'copy', '-y', output_video
        ]

        print(f"🔗 拼接视频: {' '.join(cmd)}")
        subprocess.run(cmd, check=True, capture_output=True)

        # 清理临时文件
        if os.path.exists(concat_list_path):
            os.remove(concat_list_path)

        if os.path.exists(output_video):
            print(f"✅ 视频拼接成功: {output_video}")
            return True
        else:
            print(f"❌ 视频拼接失败: 输出文件不存在")
            return False

    except Exception as e:
        print(f"❌ 视频拼接失败: {e}")
        # 清理临时文件
        if os.path.exists('temp_concat_list.txt'):
            os.remove('temp_concat_list.txt')
        return False

# ========== AI插帧处理功能 ==========

def optical_flow_interpolation(frame1, frame2, alpha=0.5, config=None):
    """
    使用光流法进行帧插值

    Args:
        frame1 (np.ndarray): 第一帧
        frame2 (np.ndarray): 第二帧
        alpha (float): 插值权重，0.5表示中间帧
        config (dict): 配置参数

    Returns:
        np.ndarray: 插值后的帧
    """
    try:
        if config is None:
            config = VIDEO_SEAMLESS_CONFIG

        # 根据配置调整帧尺寸以提高处理速度
        resize_factor = config.get('frame_resize_factor', 1.0)
        max_size = config.get('max_frame_size', (1920, 1080))
        quality = config.get('optical_flow_quality', 'high')
        blend_ratio = config.get('interpolation_blend_ratio', 0.7)

        # 调整帧尺寸
        h, w = frame1.shape[:2]
        if resize_factor != 1.0 or w > max_size[0] or h > max_size[1]:
            # 计算新尺寸
            scale = min(max_size[0]/w, max_size[1]/h, resize_factor)
            new_w, new_h = int(w * scale), int(h * scale)

            frame1_resized = cv2.resize(frame1, (new_w, new_h))
            frame2_resized = cv2.resize(frame2, (new_w, new_h))
        else:
            frame1_resized = frame1
            frame2_resized = frame2
            new_w, new_h = w, h

        # 转换为灰度图进行光流计算
        gray1 = cv2.cvtColor(frame1_resized, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(frame2_resized, cv2.COLOR_BGR2GRAY)

        # 根据质量设置选择光流算法
        if quality == 'high':
            # 使用Farneback光流（更准确但较慢）
            flow = cv2.calcOpticalFlowPyrLK(gray1, gray2, None, None,
                                          winSize=(21, 21), maxLevel=3,
                                          criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 30, 0.01))
        elif quality == 'medium':
            # 中等质量设置
            flow = cv2.calcOpticalFlowPyrLK(gray1, gray2, None, None,
                                          winSize=(15, 15), maxLevel=2,
                                          criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 20, 0.03))
        else:  # low quality
            # 快速设置
            flow = cv2.calcOpticalFlowPyrLK(gray1, gray2, None, None,
                                          winSize=(11, 11), maxLevel=1,
                                          criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT, 10, 0.1))

        # 如果光流计算失败，使用简单的线性插值
        if flow[0] is None:
            result = cv2.addWeighted(frame1_resized, 1-alpha, frame2_resized, alpha, 0)
        else:
            # 使用稠密光流进行更好的插值
            try:
                flow_dense = cv2.calcOpticalFlowFarneback(gray1, gray2, None, 0.5, 3, 15, 3, 5, 1.2, 0)

                # 创建映射网格
                y, x = np.mgrid[0:new_h, 0:new_w].astype(np.float32)

                # 计算中间帧的映射
                map_x = x + flow_dense[:, :, 0] * alpha
                map_y = y + flow_dense[:, :, 1] * alpha

                # 重映射生成中间帧
                interpolated = cv2.remap(frame1_resized, map_x, map_y, cv2.INTER_LINEAR)

                # 与简单插值结合，提高稳定性
                simple_interp = cv2.addWeighted(frame1_resized, 1-alpha, frame2_resized, alpha, 0)
                result = cv2.addWeighted(interpolated, blend_ratio, simple_interp, 1-blend_ratio, 0)

            except Exception as flow_e:
                print(f"⚠️ 稠密光流失败，使用简单插值: {flow_e}")
                result = cv2.addWeighted(frame1_resized, 1-alpha, frame2_resized, alpha, 0)

        # 如果调整了尺寸，需要恢复原始尺寸
        if result.shape[:2] != (h, w):
            result = cv2.resize(result, (w, h))

        return result

    except Exception as e:
        print(f"⚠️ 光流插值失败，使用简单插值: {e}")
        return cv2.addWeighted(frame1, 1-alpha, frame2, alpha, 0)

def advanced_frame_interpolation(frame1, frame2, num_intermediate=1, config=None):
    """
    高级帧插值，结合多种技术

    Args:
        frame1 (np.ndarray): 第一帧
        frame2 (np.ndarray): 第二帧
        num_intermediate (int): 中间帧数量
        config (dict): 配置参数

    Returns:
        list: 插值后的帧列表
    """
    if num_intermediate <= 0:
        return []

    if config is None:
        config = VIDEO_SEAMLESS_CONFIG

    interpolated_frames = []
    quality_mode = config.get('quality_vs_speed', 'balanced')

    print(f"🎯 高级插值模式: {quality_mode}, 生成 {num_intermediate} 个中间帧")

    for i in range(1, num_intermediate + 1):
        alpha = i / (num_intermediate + 1)

        if quality_mode == 'speed':
            # 快速模式：使用简单线性插值
            interp_frame = cv2.addWeighted(frame1, 1-alpha, frame2, alpha, 0)
        elif quality_mode == 'quality':
            # 质量模式：使用高质量光流插值
            quality_config = config.copy()
            quality_config['optical_flow_quality'] = 'high'
            quality_config['interpolation_blend_ratio'] = 0.8
            interp_frame = optical_flow_interpolation(frame1, frame2, alpha, quality_config)
        else:  # balanced
            # 平衡模式：使用标准光流插值
            interp_frame = optical_flow_interpolation(frame1, frame2, alpha, config)

        interpolated_frames.append(interp_frame)

    return interpolated_frames

def rife_interpolate_frames(input_dir, output_dir, multiplication_factor=2):
    """
    使用改进的RIFE风格插帧算法

    Args:
        input_dir (str): 输入帧序列目录
        output_dir (str): 输出帧序列目录
        multiplication_factor (int): 插值倍数

    Returns:
        bool: 是否成功
    """
    try:
        os.makedirs(output_dir, exist_ok=True)

        # 读取所有输入帧
        input_frames = sorted(Path(input_dir).glob('img*.png'))
        if len(input_frames) < 2:
            print(f"❌ 输入帧数不足: {len(input_frames)}")
            return False

        print(f"🤖 开始RIFE风格插帧: {len(input_frames)} 帧 -> {multiplication_factor}x")

        output_frames = []
        frame_counter = 1

        for i in range(len(input_frames) - 1):
            # 读取当前帧和下一帧
            frame1 = cv2.imread(str(input_frames[i]))
            frame2 = cv2.imread(str(input_frames[i + 1]))

            if frame1 is None or frame2 is None:
                print(f"⚠️ 无法读取帧: {input_frames[i]} 或 {input_frames[i + 1]}")
                continue

            # 添加原始帧
            output_path = os.path.join(output_dir, f'img{frame_counter:04d}.png')
            cv2.imwrite(output_path, frame1)
            frame_counter += 1

            # 生成中间帧
            if multiplication_factor > 1:
                config = VIDEO_SEAMLESS_CONFIG

                if TORCH_AVAILABLE:
                    # 使用高级插值算法
                    intermediate_frames = advanced_frame_interpolation(
                        frame1, frame2, multiplication_factor - 1, config
                    )

                    for interp_frame in intermediate_frames:
                        output_path = os.path.join(output_dir, f'img{frame_counter:04d}.png')
                        cv2.imwrite(output_path, interp_frame)
                        frame_counter += 1
                else:
                    # PyTorch不可用时使用简单插值
                    print("⚠️ PyTorch不可用，使用简单插值")
                    for j in range(1, multiplication_factor):
                        alpha = j / multiplication_factor
                        interp_frame = cv2.addWeighted(frame1, 1-alpha, frame2, alpha, 0)
                        output_path = os.path.join(output_dir, f'img{frame_counter:04d}.png')
                        cv2.imwrite(output_path, interp_frame)
                        frame_counter += 1

        # 添加最后一帧
        if len(input_frames) > 0:
            last_frame = cv2.imread(str(input_frames[-1]))
            if last_frame is not None:
                output_path = os.path.join(output_dir, f'img{frame_counter:04d}.png')
                cv2.imwrite(output_path, last_frame)
                frame_counter += 1

        print(f"✅ RIFE风格插帧完成，生成 {frame_counter-1} 帧")
        return True

    except Exception as e:
        print(f"❌ RIFE插帧失败: {e}")
        return False

def process_seam_with_ai_interpolation(frames_dir1, frames_dir2, output_dir, multiplication_factor=2):
    """
    处理两个帧序列的接缝，使用AI插帧平滑过渡

    Args:
        frames_dir1 (str): 第一个帧序列目录
        frames_dir2 (str): 第二个帧序列目录
        output_dir (str): 输出目录
        multiplication_factor (int): 插值倍数

    Returns:
        bool: 是否成功
    """
    try:
        os.makedirs(output_dir, exist_ok=True)

        # 合并两个帧序列到临时目录
        temp_combined_dir = os.path.join(output_dir, 'temp_combined')
        os.makedirs(temp_combined_dir, exist_ok=True)

        # 复制第一个序列的帧
        frames1 = sorted(Path(frames_dir1).glob('img*.png'))
        for i, frame_path in enumerate(frames1):
            shutil.copy2(frame_path, os.path.join(temp_combined_dir, f'img{i+1:04d}.png'))

        # 复制第二个序列的帧，继续编号
        frames2 = sorted(Path(frames_dir2).glob('img*.png'))
        start_index = len(frames1) + 1
        for i, frame_path in enumerate(frames2):
            shutil.copy2(frame_path, os.path.join(temp_combined_dir, f'img{start_index+i:04d}.png'))

        print(f"📁 合并帧序列: {len(frames1)} + {len(frames2)} = {len(frames1) + len(frames2)} 帧")

        # 对合并后的序列进行AI插帧
        interpolated_dir = os.path.join(output_dir, 'interpolated')
        success = rife_interpolate_frames(temp_combined_dir, interpolated_dir, multiplication_factor)

        # 清理临时目录
        if os.path.exists(temp_combined_dir):
            shutil.rmtree(temp_combined_dir)

        if success:
            # 将插帧结果移动到最终输出目录
            interpolated_frames = sorted(Path(interpolated_dir).glob('img*.png'))
            for i, frame_path in enumerate(interpolated_frames):
                final_path = os.path.join(output_dir, f'img{i+1:04d}.png')
                shutil.move(str(frame_path), final_path)

            # 清理插帧临时目录
            if os.path.exists(interpolated_dir):
                shutil.rmtree(interpolated_dir)

            print(f"✅ 接缝AI插帧处理完成")
            return True
        else:
            return False

    except Exception as e:
        print(f"❌ 接缝AI插帧处理失败: {e}")
        return False

# ========== 视频接缝处理逻辑 ==========

def process_video_seam(clip1_path, clip2_path, output_dir, seam_index):
    """
    处理两个视频片段之间的接缝

    Args:
        clip1_path (str): 第一个视频片段路径
        clip2_path (str): 第二个视频片段路径
        output_dir (str): 输出目录
        seam_index (int): 接缝索引

    Returns:
        str: 生成的平滑接缝视频路径，失败返回None
    """
    try:
        config = VIDEO_SEAMLESS_CONFIG
        fps = config['fps']
        seam_duration = config['seam_duration_seconds']
        seam_frame_count = int(fps * seam_duration)

        print(f"🔗 处理接缝 {seam_index}: {os.path.basename(clip1_path)} -> {os.path.basename(clip2_path)}")
        print(f"📊 接缝参数: FPS={fps}, 时长={seam_duration}s, 帧数={seam_frame_count}")

        # 创建工作目录
        seam_work_dir = os.path.join(output_dir, f'seam_{seam_index}_work')
        os.makedirs(seam_work_dir, exist_ok=True)

        # 1. 获取视频帧数
        total_frames_1 = get_video_frame_count(clip1_path)
        total_frames_2 = get_video_frame_count(clip2_path)

        if total_frames_1 == 0 or total_frames_2 == 0:
            print(f"❌ 无法获取视频帧数")
            return None

        # 2. 提取第一个视频的结尾帧
        seam_1_end_dir = os.path.join(seam_work_dir, 'seam_1_end')
        start_frame_1 = max(0, total_frames_1 - seam_frame_count)
        success1 = extract_frame_sequence(clip1_path, seam_1_end_dir, start_frame_1, seam_frame_count)

        if not success1:
            print(f"❌ 提取第一个视频结尾帧失败")
            return None

        # 3. 提取第二个视频的开头帧
        seam_2_start_dir = os.path.join(seam_work_dir, 'seam_2_start')
        success2 = extract_frame_sequence(clip2_path, seam_2_start_dir, 0, seam_frame_count)

        if not success2:
            print(f"❌ 提取第二个视频开头帧失败")
            return None

        # 4. AI插帧平滑处理
        smoothed_frames_dir = os.path.join(seam_work_dir, 'smoothed_frames')
        success3 = process_seam_with_ai_interpolation(
            seam_1_end_dir, seam_2_start_dir, smoothed_frames_dir,
            config['rife_multiplication_factor']
        )

        if not success3:
            print(f"❌ AI插帧平滑处理失败")
            return None

        # 5. 重新编码为视频
        smoothed_video_path = os.path.join(output_dir, f'smoothed_seam_{seam_index}.mp4')
        target_fps = fps * config['rife_multiplication_factor']
        success4 = encode_frames_to_video(
            smoothed_frames_dir, smoothed_video_path,
            target_fps, config['ffmpeg_crf_value'], config['ffmpeg_encode_preset']
        )

        if success4:
            print(f"✅ 接缝 {seam_index} 处理完成: {smoothed_video_path}")
            # 清理工作目录
            if os.path.exists(seam_work_dir):
                shutil.rmtree(seam_work_dir)
            return smoothed_video_path
        else:
            print(f"❌ 接缝视频编码失败")
            return None

    except Exception as e:
        print(f"❌ 处理接缝 {seam_index} 失败: {e}")
        return None

def trim_video_for_seamless(video_path, output_path, trim_start=True, trim_end=True):
    """
    为无缝拼接裁剪视频

    Args:
        video_path (str): 输入视频路径
        output_path (str): 输出视频路径
        trim_start (bool): 是否裁剪开头
        trim_end (bool): 是否裁剪结尾

    Returns:
        bool: 是否成功
    """
    try:
        config = VIDEO_SEAMLESS_CONFIG
        fps = config['fps']
        seam_duration = config['seam_duration_seconds']
        seam_frame_count = int(fps * seam_duration)

        total_frames = get_video_frame_count(video_path)
        if total_frames == 0:
            return False

        # 计算裁剪范围
        start_frame = seam_frame_count if trim_start else 0
        end_frame = total_frames - seam_frame_count - 1 if trim_end else total_frames - 1

        if start_frame >= end_frame:
            print(f"❌ 裁剪范围无效: start={start_frame}, end={end_frame}, total={total_frames}")
            return False

        print(f"✂️ 裁剪视频: 帧 {start_frame} 到 {end_frame} (总帧数: {total_frames})")
        return trim_video(video_path, output_path, start_frame, end_frame)

    except Exception as e:
        print(f"❌ 裁剪视频失败: {e}")
        return False

# ========== 视频无缝拼接主函数 ==========

def advanced_seamless_concatenation(video_paths, output_video_path):
    """
    高级无缝拼接 - 解决帧重复和运动不连续问题
    通过精确的时间计算和帧分析，确保真正的无缝效果
    """
    try:
        if len(video_paths) < 2:
            print("❌ 至少需要2个视频才能进行拼接")
            return False

        print(f"🎬 开始高级无缝拼接 {len(video_paths)} 个视频")
        print(f"🎯 目标: 消除帧重复，实现真正的运动连续性")

        # 分析每个视频的详细信息
        video_infos = []
        for i, video_path in enumerate(video_paths):
            info = get_video_info(video_path)
            if info:
                video_infos.append(info)
                print(f"📊 视频{i+1}: {info['duration']:.1f}s, {info['total_frames']}帧, {info['fps']}fps")
            else:
                print(f"❌ 无法获取视频{i+1}信息")
                return False

        # 使用更精确的帧级别拼接
        return frame_level_seamless_concat(video_paths, video_infos, output_video_path)

    except Exception as e:
        print(f"❌ 高级无缝拼接失败: {e}")
        return False

def frame_level_seamless_concat(video_paths, video_infos, output_video_path):
    """
    帧级别的无缝拼接 - 精确控制每一帧的过渡
    """
    try:
        print(f"🔬 执行帧级别无缝拼接...")

        # 创建临时工作目录
        work_dir = f"temp_frame_concat_{int(time.time())}"
        os.makedirs(work_dir, exist_ok=True)

        # 为每个视频创建修剪版本，去除重复帧
        trimmed_videos = []
        transition_frames = 12  # 过渡帧数

        for i, (video_path, info) in enumerate(zip(video_paths, video_infos)):
            trimmed_path = os.path.join(work_dir, f'trimmed_{i}.mp4')

            if i == 0:
                # 第一个视频：保留完整长度，但为过渡预留空间
                duration = info['duration'] - (transition_frames / info['fps'])
                cmd = [
                    'ffmpeg', '-y',
                    '-i', video_path,
                    '-t', str(duration),
                    '-c', 'copy',
                    trimmed_path
                ]
            elif i == len(video_paths) - 1:
                # 最后一个视频：跳过开头的过渡帧
                start_time = transition_frames / info['fps']
                cmd = [
                    'ffmpeg', '-y',
                    '-ss', str(start_time),
                    '-i', video_path,
                    '-c', 'copy',
                    trimmed_path
                ]
            else:
                # 中间视频：两端都处理
                start_time = transition_frames / info['fps']
                duration = info['duration'] - 2 * (transition_frames / info['fps'])
                cmd = [
                    'ffmpeg', '-y',
                    '-ss', str(start_time),
                    '-t', str(duration),
                    '-i', video_path,
                    '-c', 'copy',
                    trimmed_path
                ]

            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                trimmed_videos.append(trimmed_path)
                print(f"✅ 视频{i+1}修剪完成")
            else:
                print(f"❌ 视频{i+1}修剪失败: {result.stderr}")
                return False

        # 创建平滑过渡片段
        transition_videos = []
        for i in range(len(video_paths) - 1):
            transition_path = os.path.join(work_dir, f'transition_{i}.mp4')
            success = create_smooth_transition(
                video_paths[i], video_paths[i+1],
                transition_path, transition_frames, video_infos[i]['fps']
            )
            if success:
                transition_videos.append(transition_path)
                print(f"✅ 过渡{i}创建完成")
            else:
                print(f"❌ 过渡{i}创建失败")
                return False

        # 最终拼接：修剪视频 + 过渡片段
        final_list = []
        for i in range(len(trimmed_videos)):
            final_list.append(trimmed_videos[i])
            if i < len(transition_videos):
                final_list.append(transition_videos[i])

        # 执行最终拼接
        success = concat_videos(final_list, output_video_path)

        # 清理临时文件
        if os.path.exists(work_dir):
            shutil.rmtree(work_dir)

        if success:
            print(f"✅ 帧级别无缝拼接完成: {output_video_path}")
            return True
        else:
            print(f"❌ 最终拼接失败")
            return False

    except Exception as e:
        print(f"❌ 帧级别拼接失败: {e}")
        return False

def create_smooth_transition(video1_path, video2_path, output_path, transition_frames, fps):
    """
    创建两个视频之间的平滑过渡片段
    """
    try:
        print(f"🌊 创建平滑过渡: {transition_frames}帧")

        # 提取第一个视频的尾部帧
        end_frames_dir = f"temp_end_frames_{int(time.time())}"
        os.makedirs(end_frames_dir, exist_ok=True)

        # 获取视频1的最后几帧
        info1 = get_video_info(video1_path)
        start_time1 = info1['duration'] - (transition_frames / fps)

        cmd1 = [
            'ffmpeg', '-y',
            '-ss', str(start_time1),
            '-i', video1_path,
            '-frames:v', str(transition_frames // 2),
            '-q:v', '2',
            os.path.join(end_frames_dir, 'end_%03d.jpg')
        ]

        result1 = subprocess.run(cmd1, capture_output=True, text=True)
        if result1.returncode != 0:
            print(f"❌ 提取尾部帧失败")
            return False

        # 获取视频2的开头几帧
        cmd2 = [
            'ffmpeg', '-y',
            '-i', video2_path,
            '-frames:v', str(transition_frames // 2),
            '-q:v', '2',
            os.path.join(end_frames_dir, 'start_%03d.jpg')
        ]

        result2 = subprocess.run(cmd2, capture_output=True, text=True)
        if result2.returncode != 0:
            print(f"❌ 提取开头帧失败")
            return False

        # 使用FFmpeg的minterpolate创建平滑过渡
        temp_video = f"temp_transition_{int(time.time())}.mp4"

        # 先创建一个包含所有帧的视频
        cmd3 = [
            'ffmpeg', '-y',
            '-framerate', str(fps // 2),  # 降低帧率为插值留空间
            '-pattern_type', 'glob',
            '-i', os.path.join(end_frames_dir, '*.jpg'),
            '-c:v', 'libx264',
            '-pix_fmt', 'yuv420p',
            temp_video
        ]

        result3 = subprocess.run(cmd3, capture_output=True, text=True)
        if result3.returncode != 0:
            print(f"❌ 创建临时视频失败")
            return False

        # 应用插值创建平滑过渡
        cmd4 = [
            'ffmpeg', '-y',
            '-i', temp_video,
            '-filter:v', f'minterpolate=fps={fps}:mi_mode=mci',
            '-c:v', 'libx264',
            '-preset', 'fast',
            '-crf', '23',
            output_path
        ]

        result4 = subprocess.run(cmd4, capture_output=True, text=True)

        # 清理临时文件
        if os.path.exists(end_frames_dir):
            shutil.rmtree(end_frames_dir)
        if os.path.exists(temp_video):
            os.remove(temp_video)

        if result4.returncode == 0:
            print(f"✅ 平滑过渡创建成功")
            return True
        else:
            print(f"❌ 平滑过渡创建失败: {result4.stderr}")
            return False

    except Exception as e:
        print(f"❌ 创建平滑过渡异常: {e}")
        return False

def simple_seamless_concatenation(video_paths, output_video_path):
    """
    简化的无缝拼接，使用FFmpeg的交叉淡化效果

    Args:
        video_paths (list): 视频文件路径列表
        output_video_path (str): 输出视频路径

    Returns:
        bool: 是否成功
    """
    try:
        if len(video_paths) < 2:
            print("❌ 至少需要2个视频才能进行拼接")
            return False

        print(f"🎬 开始智能无缝拼接 {len(video_paths)} 个视频")
        print(f"🎯 目标: 消除帧重复，实现真正的运动连续性")

        # 使用FFmpeg的xfade滤镜进行交叉淡化拼接
        fade_duration = 0.5  # 淡化时长（秒）

        # 首先分析视频时长，计算合适的偏移时间
        video_durations = []
        for video_path in video_paths:
            info = get_video_info(video_path)
            if info:
                video_durations.append(info['duration'])
            else:
                video_durations.append(6.0)  # 默认6秒

        print(f"📊 视频时长: {[f'{d:.1f}s' for d in video_durations]}")

        # 计算每个视频的实际偏移时间（避免重复帧）
        offsets = []
        cumulative_time = 0
        for i, duration in enumerate(video_durations):
            if i == 0:
                offsets.append(0)
                cumulative_time = duration - fade_duration  # 第一个视频减去淡化时长
            else:
                offsets.append(cumulative_time)
                if i < len(video_durations) - 1:  # 不是最后一个视频
                    cumulative_time += duration - fade_duration
                else:  # 最后一个视频
                    cumulative_time += duration

        print(f"📊 计算的偏移时间: {[f'{o:.1f}s' for o in offsets]}")

        # 构建FFmpeg命令
        inputs = []
        filter_complex = []

        # 添加输入文件
        for i, video_path in enumerate(video_paths):
            inputs.extend(['-i', video_path])

        # 检查每个视频的信息
        video_infos = []
        has_audio = []
        for video_path in video_paths:
            video_info = get_video_info(video_path)
            video_infos.append(video_info)
            has_audio.append(video_info and video_info.get('audio_codec', 'unknown') != 'unknown')

        # 检查分辨率是否一致
        resolutions = [(info['width'], info['height']) for info in video_infos if info]
        if len(set(resolutions)) > 1:
            print(f"⚠️ 检测到不同分辨率: {resolutions}")
            print(f"🔧 统一分辨率到第一个视频的尺寸: {resolutions[0]}")
            target_width, target_height = resolutions[0]

            # 添加分辨率统一滤镜
            for i in range(len(video_paths)):
                if i == 0:
                    filter_complex.append(f'[{i}:v]scale={target_width}:{target_height}[v{i}_scaled]')
                else:
                    filter_complex.append(f'[{i}:v]scale={target_width}:{target_height}[v{i}_scaled]')

            # 构建交叉淡化滤镜链（使用缩放后的视频）
            current_output = 'v0_scaled'
            for i in range(1, len(video_paths)):
                offset_time = offsets[i]
                if i == 1:
                    filter_complex.append(f'[v0_scaled][v{i}_scaled]xfade=transition=fade:duration={fade_duration}:offset={offset_time}[v{i}]')
                    current_output = f'v{i}'
                else:
                    filter_complex.append(f'[{current_output}][v{i}_scaled]xfade=transition=fade:duration={fade_duration}:offset={offset_time}[v{i}]')
                    current_output = f'v{i}'
        else:
            print(f"✅ 所有视频分辨率一致: {resolutions[0]}")
            # 构建滤镜链（使用计算的偏移时间）
            current_output = '0:v'
            for i in range(1, len(video_paths)):
                offset_time = offsets[i]
                if i == 1:
                    # 第一个交叉淡化
                    filter_complex.append(f'[0:v][1:v]xfade=transition=fade:duration={fade_duration}:offset={offset_time}[v{i}]')
                    current_output = f'v{i}'
                else:
                    # 后续的交叉淡化
                    filter_complex.append(f'[{current_output}][{i}:v]xfade=transition=fade:duration={fade_duration}:offset={offset_time}[v{i}]')
                    current_output = f'v{i}'

        # 处理音频（如果所有视频都有音频）
        if all(has_audio):
            # 音频处理 - 简单拼接
            audio_inputs = [f'{i}:a' for i in range(len(video_paths))]
            filter_complex.append(f'[{"][".join(audio_inputs)}]concat=n={len(video_paths)}:v=0:a=1[aout]')

        # 构建完整命令
        cmd = ['ffmpeg', '-y'] + inputs + [
            '-filter_complex', ';'.join(filter_complex),
            '-map', f'[{current_output}]'
        ]

        if all(has_audio):
            cmd.extend(['-map', '[aout]', '-c:a', 'aac'])

        cmd.extend([
            '-c:v', 'libx264',
            '-preset', 'fast',
            '-crf', '25',
            output_video_path
        ])

        print(f"🔧 执行FFmpeg命令...")
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print(f"✅ 简化无缝拼接完成: {output_video_path}")
            return True
        else:
            print(f"❌ FFmpeg拼接失败: {result.stderr}")
            # 如果交叉淡化失败，回退到简单拼接
            return concat_videos(video_paths, output_video_path)

    except Exception as e:
        print(f"❌ 简化无缝拼接失败: {e}")
        # 回退到简单拼接
        return concat_videos(video_paths, output_video_path)

def seamless_video_concatenation(video_paths, output_video_path, temp_dir=None):
    """
    无缝拼接多个视频 - 优化版本

    Args:
        video_paths (list): 视频文件路径列表
        output_video_path (str): 输出视频路径
        temp_dir (str): 临时工作目录（可选）

    Returns:
        bool: 是否成功
    """
    try:
        if len(video_paths) < 2:
            print("❌ 至少需要2个视频才能进行拼接")
            return False

        config = VIDEO_SEAMLESS_CONFIG

        # 检查拼接模式
        if config.get('enable_ai_interpolation', False):
            print("🤖 使用AI插帧无缝拼接模式")
            return complex_seamless_concatenation(video_paths, output_video_path, temp_dir)
        elif config.get('enable_ultimate_mode', False):
            print("🚀 使用终极无缝拼接模式 V3.0")
            return ultimate_seamless_concatenation(video_paths, output_video_path)
        else:
            print("⚡ 使用高级无缝拼接模式")
            return advanced_seamless_concatenation(video_paths, output_video_path)

    except Exception as e:
        print(f"❌ 无缝拼接失败: {e}")
        return False

def ultimate_seamless_concatenation(video_paths, output_video_path):
    """
    终极无缝拼接 V3.0 - 追求绝对无缝
    使用光流运动分析 + AI帧插值 + 运动匹配融合

    Args:
        video_paths (list): 视频文件路径列表
        output_video_path (str): 输出视频路径

    Returns:
        bool: 是否成功
    """
    try:
        if len(video_paths) < 2:
            print("❌ 至少需要2个视频才能进行终极拼接")
            return False

        config = ULTIMATE_SEAMLESS_CONFIG
        fps = config['fps']
        bridge_duration = config['bridge_duration_seconds']
        half_bridge_frames = int(fps * bridge_duration / 2)

        print(f"🚀 开始终极无缝拼接 V3.0")
        print(f"📊 参数: FPS={fps}, 桥接时长={bridge_duration}s, 半桥帧数={half_bridge_frames}")

        # 创建工作目录
        work_dir = os.path.join(config['temp_dir'], f'ultimate_concat_{int(time.time())}')
        os.makedirs(work_dir, exist_ok=True)

        # 处理每个接缝
        processed_segments = []

        for i in range(len(video_paths)):
            if i == 0:
                # 第一个视频：裁剪掉最后的半桥帧数
                trimmed_path = os.path.join(work_dir, f'trimmed_clip_{i}.mp4')
                success = trim_video_end(video_paths[i], trimmed_path, half_bridge_frames)
                if success:
                    processed_segments.append(trimmed_path)
                else:
                    return False
            elif i == len(video_paths) - 1:
                # 最后一个视频：裁剪掉最前的半桥帧数
                trimmed_path = os.path.join(work_dir, f'trimmed_clip_{i}.mp4')
                success = trim_video_start(video_paths[i], trimmed_path, half_bridge_frames)
                if success:
                    processed_segments.append(trimmed_path)
                else:
                    return False
            else:
                # 中间视频：两端都裁剪
                trimmed_path = os.path.join(work_dir, f'trimmed_clip_{i}.mp4')
                success = trim_video_both_ends(video_paths[i], trimmed_path, half_bridge_frames)
                if success:
                    processed_segments.append(trimmed_path)
                else:
                    return False

            # 为每个接缝生成桥接视频
            if i < len(video_paths) - 1:
                bridge_path = os.path.join(work_dir, f'bridge_{i}_{i+1}.mp4')
                success = create_ultimate_bridge(
                    video_paths[i], video_paths[i+1],
                    bridge_path, work_dir, i
                )
                if success:
                    processed_segments.append(bridge_path)
                else:
                    return False

        # 最终拼接
        print(f"\n🔗 执行最终拼接...")
        success = concat_videos(processed_segments, output_video_path)

        if success:
            print(f"✅ 终极无缝拼接完成: {output_video_path}")
            # 清理临时文件
            if os.path.exists(work_dir):
                shutil.rmtree(work_dir)
            return True
        else:
            print(f"❌ 最终拼接失败")
            return False

    except Exception as e:
        print(f"❌ 终极无缝拼接失败: {e}")
        return False

def create_ultimate_bridge(clip_a_path, clip_b_path, bridge_output_path, work_dir, bridge_index):
    """
    创建终极桥接视频 - 核心算法实现

    Args:
        clip_a_path (str): 第一个视频路径
        clip_b_path (str): 第二个视频路径
        bridge_output_path (str): 桥接视频输出路径
        work_dir (str): 工作目录
        bridge_index (int): 桥接索引

    Returns:
        bool: 是否成功
    """
    try:
        config = ULTIMATE_SEAMLESS_CONFIG
        fps = config['fps']
        bridge_duration = config['bridge_duration_seconds']
        half_bridge_frames = int(fps * bridge_duration / 2)
        rife_factor = config['rife_multiplication_factor']

        print(f"\n🔗 创建终极桥接 {bridge_index}: {os.path.basename(clip_a_path)} -> {os.path.basename(clip_b_path)}")

        # 步骤一：素材预处理 - 识别"手术区域"
        a_end_dir = os.path.join(work_dir, f'bridge_{bridge_index}_a_end')
        b_start_dir = os.path.join(work_dir, f'bridge_{bridge_index}_b_start')
        seam_dir = os.path.join(work_dir, f'bridge_{bridge_index}_seam_to_fix')

        os.makedirs(a_end_dir, exist_ok=True)
        os.makedirs(b_start_dir, exist_ok=True)
        os.makedirs(seam_dir, exist_ok=True)

        print(f"📋 步骤一：提取手术素材 (各{half_bridge_frames}帧)")

        # 提取clip_A的最后12帧
        success1 = extract_video_end_frames(clip_a_path, a_end_dir, half_bridge_frames)
        if not success1:
            print(f"❌ 提取clip_A结尾帧失败")
            return False

        # 提取clip_B的最前12帧
        success2 = extract_video_start_frames(clip_b_path, b_start_dir, half_bridge_frames)
        if not success2:
            print(f"❌ 提取clip_B开头帧失败")
            return False

        # 合并成24帧序列
        success3 = merge_frame_sequences(a_end_dir, b_start_dir, seam_dir)
        if not success3:
            print(f"❌ 合并帧序列失败")
            return False

        # 步骤二：AI运动修复与桥接
        print(f"🤖 步骤二：AI运动修复与桥接")

        # 2.1 动作预测与插值 (RIFE)
        rife_output_dir = os.path.join(work_dir, f'bridge_{bridge_index}_rife_output')
        success4 = apply_rife_interpolation(seam_dir, rife_output_dir, rife_factor)
        if not success4:
            print(f"❌ RIFE插值失败")
            return False

        # 2.2 光流运动匹配
        motion_corrected_dir = os.path.join(work_dir, f'bridge_{bridge_index}_motion_corrected')
        success5 = apply_optical_flow_correction(rife_output_dir, motion_corrected_dir, half_bridge_frames)
        if not success5:
            print(f"❌ 光流运动匹配失败")
            return False

        # 2.3 时间重映射与融合
        final_frames_dir = os.path.join(work_dir, f'bridge_{bridge_index}_final_frames')
        success6 = apply_time_remapping(motion_corrected_dir, final_frames_dir, fps * bridge_duration)
        if not success6:
            print(f"❌ 时间重映射失败")
            return False

        # 步骤三：生成最终桥接视频
        print(f"🎬 步骤三：生成最终桥接视频")
        success7 = encode_frames_to_video(final_frames_dir, bridge_output_path, fps, 23, 'medium')

        if success7:
            print(f"✅ 桥接 {bridge_index} 创建成功")
            return True
        else:
            print(f"❌ 桥接视频编码失败")
            return False

    except Exception as e:
        print(f"❌ 创建终极桥接失败: {e}")
        return False

def trim_video_end(input_path, output_path, frames_to_remove):
    """裁剪视频结尾的指定帧数"""
    try:
        # 获取视频总帧数
        total_frames = get_video_frame_count(input_path)
        if total_frames <= frames_to_remove:
            print(f"❌ 视频帧数不足，无法裁剪")
            return False

        # 计算保留的帧数
        keep_frames = total_frames - frames_to_remove
        fps = get_video_fps(input_path)
        duration = keep_frames / fps

        cmd = [
            'ffmpeg', '-y',
            '-i', input_path,
            '-t', str(duration),
            '-c', 'copy',
            output_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)
        return result.returncode == 0

    except Exception as e:
        print(f"❌ 裁剪视频结尾失败: {e}")
        return False

def trim_video_start(input_path, output_path, frames_to_remove):
    """裁剪视频开头的指定帧数"""
    try:
        fps = get_video_fps(input_path)
        start_time = frames_to_remove / fps

        cmd = [
            'ffmpeg', '-y',
            '-ss', str(start_time),
            '-i', input_path,
            '-c', 'copy',
            output_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)
        return result.returncode == 0

    except Exception as e:
        print(f"❌ 裁剪视频开头失败: {e}")
        return False

def trim_video_both_ends(input_path, output_path, frames_to_remove):
    """裁剪视频两端的指定帧数"""
    try:
        total_frames = get_video_frame_count(input_path)
        if total_frames <= frames_to_remove * 2:
            print(f"❌ 视频帧数不足，无法裁剪两端")
            return False

        fps = get_video_fps(input_path)
        start_time = frames_to_remove / fps
        keep_frames = total_frames - frames_to_remove * 2
        duration = keep_frames / fps

        cmd = [
            'ffmpeg', '-y',
            '-ss', str(start_time),
            '-t', str(duration),
            '-i', input_path,
            '-c', 'copy',
            output_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)
        return result.returncode == 0

    except Exception as e:
        print(f"❌ 裁剪视频两端失败: {e}")
        return False

def get_video_fps(video_path):
    """获取视频帧率"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet',
            '-select_streams', 'v:0',
            '-show_entries', 'stream=r_frame_rate',
            '-of', 'csv=p=0',
            video_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            fps_str = result.stdout.strip()
            if '/' in fps_str:
                num, den = fps_str.split('/')
                return float(num) / float(den)
            else:
                return float(fps_str)
        return 24.0  # 默认帧率

    except Exception as e:
        print(f"❌ 获取视频帧率失败: {e}")
        return 24.0

def get_video_frame_count(video_path):
    """获取视频总帧数"""
    try:
        cmd = [
            'ffprobe', '-v', 'quiet',
            '-select_streams', 'v:0',
            '-show_entries', 'stream=nb_frames',
            '-of', 'csv=p=0',
            video_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            frame_count = result.stdout.strip()
            if frame_count and frame_count != 'N/A':
                return int(frame_count)

        # 如果无法直接获取，通过时长和帧率计算
        duration_cmd = [
            'ffprobe', '-v', 'quiet',
            '-show_entries', 'format=duration',
            '-of', 'csv=p=0',
            video_path
        ]

        duration_result = subprocess.run(duration_cmd, capture_output=True, text=True)
        if duration_result.returncode == 0:
            duration = float(duration_result.stdout.strip())
            fps = get_video_fps(video_path)
            return int(duration * fps)

        return 144  # 默认值（6秒 * 24fps）

    except Exception as e:
        print(f"❌ 获取视频帧数失败: {e}")
        return 144

def detect_frame_similarity(frame1_path, frame2_path):
    """
    检测两帧的相似度，用于避免重复帧
    返回相似度分数 (0-1)，1表示完全相同
    """
    try:
        import cv2
        import numpy as np

        # 读取两帧
        img1 = cv2.imread(frame1_path, cv2.IMREAD_GRAYSCALE)
        img2 = cv2.imread(frame2_path, cv2.IMREAD_GRAYSCALE)

        if img1 is None or img2 is None:
            return 0.0

        # 调整到相同尺寸
        h, w = img1.shape
        img2 = cv2.resize(img2, (w, h))

        # 计算像素差异
        diff = cv2.absdiff(img1, img2)
        similarity = 1.0 - (np.mean(diff) / 255.0)

        return similarity

    except Exception as e:
        print(f"⚠️ 帧相似度检测失败: {e}")
        return 0.0  # 无法检测时假设不相似

def extract_video_end_frames(video_path, output_dir, frame_count):
    """提取视频结尾的指定帧数"""
    try:
        total_frames = get_video_frame_count(video_path)
        fps = get_video_fps(video_path)

        # V3.2关键修复：避免最后一帧（与下个视频首帧重复的根本原因）
        exclude_last_frames = 1  # 排除最后1帧（关键！）
        buffer_frames = 8  # 额外安全缓冲

        # 确保不提取最后一帧
        end_frame = total_frames - exclude_last_frames
        start_frame = max(0, end_frame - frame_count - buffer_frames)
        start_time = start_frame / fps

        print(f"📊 视频总帧数: {total_frames}")
        print(f"🎯 关键修复: 排除最后{exclude_last_frames}帧，避免重复")
        print(f"🎬 安全提取范围: 帧{start_frame}-{start_frame + frame_count}")

        cmd = [
            'ffmpeg', '-y',
            '-ss', str(start_time),
            '-i', video_path,
            '-frames:v', str(frame_count),
            '-q:v', '2',
            os.path.join(output_dir, 'frame_%04d.jpg')
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)
        return result.returncode == 0

    except Exception as e:
        print(f"❌ 提取视频结尾帧失败: {e}")
        return False

def extract_video_start_frames(video_path, output_dir, frame_count):
    """
    提取视频开头的指定帧数 - V3.1反卡顿优化
    跳过开头几帧避免重复
    """
    try:
        fps = get_video_fps(video_path)

        # V3.2关键修复：跳过更多开头帧，确保与前一个视频无重复
        skip_frames = 5  # 增加跳过帧数
        start_time = skip_frames / fps

        print(f"📊 关键修复: 跳过开头{skip_frames}帧，从{start_time:.2f}s开始提取")
        print(f"🎯 目标: 确保与前一个视频的尾帧完全不同")

        cmd = [
            'ffmpeg', '-y',
            '-ss', str(start_time),
            '-i', video_path,
            '-frames:v', str(frame_count),
            '-q:v', '1',  # 最高质量
            '-pix_fmt', 'yuv420p',  # 确保兼容性
            os.path.join(output_dir, 'frame_%04d.jpg')
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ 成功提取{frame_count}帧头部素材")
            return True
        else:
            print(f"❌ 提取失败: {result.stderr}")
            return False

    except Exception as e:
        print(f"❌ 提取视频开头帧失败: {e}")
        return False

def merge_frame_sequences(a_end_dir, b_start_dir, output_dir):
    """合并两个帧序列"""
    try:
        import glob

        # 获取A序列的帧
        a_frames = sorted(glob.glob(os.path.join(a_end_dir, '*.jpg')))
        b_frames = sorted(glob.glob(os.path.join(b_start_dir, '*.jpg')))

        frame_index = 1

        # 复制A序列的帧
        for frame_path in a_frames:
            output_path = os.path.join(output_dir, f'frame_{frame_index:04d}.jpg')
            shutil.copy2(frame_path, output_path)
            frame_index += 1

        # 复制B序列的帧
        for frame_path in b_frames:
            output_path = os.path.join(output_dir, f'frame_{frame_index:04d}.jpg')
            shutil.copy2(frame_path, output_path)
            frame_index += 1

        print(f"✅ 合并帧序列完成: {frame_index-1} 帧")

        # V3.2新增：检测并报告帧重复情况
        try:
            # 检查中间的过渡帧（video1尾帧 vs video2首帧）
            mid_point = frame_index // 2

            # 构建帧路径
            frame1_path = os.path.join(output_dir, f'frame_{mid_point-1:04d}.jpg')
            frame2_path = os.path.join(output_dir, f'frame_{mid_point:04d}.jpg')

            if os.path.exists(frame1_path) and os.path.exists(frame2_path):
                similarity = detect_frame_similarity(frame1_path, frame2_path)
                print(f"🔍 帧重复检测: 过渡帧相似度 {similarity:.3f}")

                if similarity > 0.9:
                    print(f"⚠️ 检测到高相似度帧({similarity:.3f})，可能存在重复")
                    print(f"💡 建议: 这可能导致拼接卡顿")
                else:
                    print(f"✅ 帧差异良好({similarity:.3f})，无明显重复")
        except Exception as e:
            print(f"⚠️ 帧重复检测失败: {e}")

        return True

    except Exception as e:
        print(f"❌ 合并帧序列失败: {e}")
        return False

def encode_frames_to_video(frames_dir, output_path, fps, crf=23, preset='medium'):
    """将帧序列编码为视频"""
    try:
        cmd = [
            'ffmpeg', '-y',
            '-framerate', str(fps),
            '-i', os.path.join(frames_dir, 'frame_%04d.jpg'),
            '-c:v', 'libx264',
            '-preset', preset,
            '-crf', str(crf),
            '-pix_fmt', 'yuv420p',
            output_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)
        return result.returncode == 0

    except Exception as e:
        print(f"❌ 编码帧序列为视频失败: {e}")
        return False

def apply_rife_interpolation(input_dir, output_dir, multiplication_factor):
    """
    应用RIFE AI插值 - 按照技术文档V3.0实现
    目标：生成约95帧的超平滑序列（24帧 * 4倍 ≈ 96帧）
    """
    try:
        print(f"🤖 步骤2.1: 动作预测与插值 (RIFE)")
        print(f"📊 目标: 生成约{24 * multiplication_factor}帧的超平滑序列")

        import glob
        input_frames = sorted(glob.glob(os.path.join(input_dir, '*.jpg')))

        if len(input_frames) == 0:
            print(f"❌ 没有找到输入帧")
            return False

        os.makedirs(output_dir, exist_ok=True)

        print(f"📋 输入: {len(input_frames)}帧 -> 目标: ~{len(input_frames) * multiplication_factor}帧")

        # 使用高级的RIFE模拟算法
        # 由于真正的RIFE需要特殊环境，我们使用FFmpeg的最高质量插值来模拟

        # 步骤1: 创建高质量临时视频
        temp_video = os.path.join(os.path.dirname(input_dir), 'rife_input.mp4')

        # 使用低帧率输入，为插值留出最大空间
        input_fps = 6  # 极低帧率，模拟RIFE的输入条件
        cmd1 = [
            'ffmpeg', '-y',
            '-framerate', str(input_fps),
            '-i', os.path.join(input_dir, 'frame_%04d.jpg'),
            '-c:v', 'libx264',
            '-pix_fmt', 'yuv420p',
            '-preset', 'veryslow',  # 最高质量
            '-crf', '15',  # 极高质量
            temp_video
        ]

        print(f"🔧 创建RIFE输入视频: {input_fps}fps (极低帧率)")
        result1 = subprocess.run(cmd1, capture_output=True, text=True)
        if result1.returncode != 0:
            print(f"❌ 创建输入视频失败: {result1.stderr}")
            return False

        # 步骤2: 应用RIFE级别的高倍率插值
        target_fps = input_fps * multiplication_factor * 6  # 额外6倍，模拟RIFE的高倍率
        rife_output = os.path.join(os.path.dirname(input_dir), 'rife_output.mp4')

        print(f"🚀 执行RIFE级别插值: {input_fps}fps -> {target_fps}fps (约{multiplication_factor*6}倍)")

        # 使用最高质量的运动插值，模拟RIFE效果
        cmd2 = [
            'ffmpeg', '-y',
            '-i', temp_video,
            '-filter:v', f'minterpolate=fps={target_fps}:mi_mode=mci:mc_mode=aobmc:me_mode=bidir:vsbmc=1',
            '-c:v', 'libx264',
            '-preset', 'veryslow',
            '-crf', '16',
            rife_output
        ]

        result2 = subprocess.run(cmd2, capture_output=True, text=True)
        if result2.returncode != 0:
            print(f"❌ RIFE级别插值失败，尝试备用方案...")
            print(f"错误: {result2.stderr}")

            # 备用方案：使用多步插值
            intermediate_fps = input_fps * 2
            cmd2_backup = [
                'ffmpeg', '-y',
                '-i', temp_video,
                '-filter:v', f'minterpolate=fps={intermediate_fps}:mi_mode=mci',
                '-c:v', 'libx264',
                '-preset', 'fast',
                '-crf', '18',
                rife_output
            ]

            result2 = subprocess.run(cmd2_backup, capture_output=True, text=True)
            if result2.returncode != 0:
                print(f"❌ 备用插值也失败")
                return False
            else:
                print(f"✅ 备用插值成功: {intermediate_fps}fps")
        else:
            print(f"✅ RIFE级别插值成功: {target_fps}fps")

        # 步骤3: 提取超高帧率序列
        print(f"📤 提取超平滑帧序列...")
        cmd3 = [
            'ffmpeg', '-y',
            '-i', rife_output,
            '-q:v', '1',  # 最高质量
            os.path.join(output_dir, 'frame_%04d.jpg')
        ]

        result3 = subprocess.run(cmd3, capture_output=True, text=True)

        # 清理临时文件
        if os.path.exists(temp_video):
            os.remove(temp_video)
        if os.path.exists(rife_output):
            os.remove(rife_output)

        if result3.returncode == 0:
            # 验证输出
            output_frames = sorted(glob.glob(os.path.join(output_dir, '*.jpg')))
            print(f"✅ RIFE插值完成: {len(input_frames)}帧 -> {len(output_frames)}帧")
            print(f"📊 实现了约{len(output_frames)/len(input_frames):.1f}倍插值")

            if len(output_frames) >= len(input_frames) * 2:
                print(f"🎉 成功生成超平滑序列，帧率提升显著")
                return True
            else:
                print(f"⚠️ 插值效果有限，但仍然有改善")
                return True
        else:
            print(f"❌ 帧提取失败: {result3.stderr}")
            return False

    except Exception as e:
        print(f"❌ RIFE插值异常: {e}")
        return False

def apply_optical_flow_correction(input_dir, output_dir, half_bridge_frames):
    """
    步骤2.2: 光流运动匹配 (OpenCV) - 按照技术文档V3.0实现
    目标: 让过渡的后半段（源自Clip B）的运动，完美匹配前半段（源自Clip A）的运动趋势
    """
    try:
        print(f"🌊 步骤2.2: 光流运动匹配 (OpenCV)")
        print(f"🎯 目标: 校准运动趋势，实现完美的动作连续性")

        import glob
        frames = sorted(glob.glob(os.path.join(input_dir, '*.jpg')))

        if len(frames) == 0:
            print(f"❌ 没有找到输入帧")
            return False

        os.makedirs(output_dir, exist_ok=True)

        try:
            import cv2
            import numpy as np

            print(f"📊 分析{len(frames)}帧的运动模式...")

            # 步骤a: 加载所有帧
            frame_images = []
            for frame_path in frames:
                img = cv2.imread(frame_path)
                if img is not None:
                    frame_images.append((frame_path, img))

            if len(frame_images) < 4:
                print(f"⚠️ 帧数太少，跳过光流分析")
                for i, (frame_path, _) in enumerate(frame_images):
                    output_path = os.path.join(output_dir, f'frame_{i+1:04d}.jpg')
                    shutil.copy2(frame_path, output_path)
                return True

            # 步骤b: 计算前半段序列（源自Clip A）的平均光流矢量
            mid_point = len(frame_images) // 2
            clip_a_frames = frame_images[:mid_point]
            clip_b_frames = frame_images[mid_point:]

            print(f"📋 分析前半段({len(clip_a_frames)}帧)的运动趋势...")

            # 计算Clip A的平均光流
            flow_vectors = []
            for i in range(len(clip_a_frames) - 1):
                gray1 = cv2.cvtColor(clip_a_frames[i][1], cv2.COLOR_BGR2GRAY)
                gray2 = cv2.cvtColor(clip_a_frames[i+1][1], cv2.COLOR_BGR2GRAY)

                # 使用Farneback光流算法（更稳定）
                flow = cv2.calcOpticalFlowFarneback(gray1, gray2, None, 0.5, 3, 15, 3, 5, 1.2, 0)
                if flow is not None:
                    # 计算平均流向量
                    mean_flow_x = np.mean(flow[:, :, 0])
                    mean_flow_y = np.mean(flow[:, :, 1])
                    flow_vectors.append([mean_flow_x, mean_flow_y])

            if len(flow_vectors) == 0:
                print(f"⚠️ 无法计算光流，使用直接复制")
                for i, (frame_path, _) in enumerate(frame_images):
                    output_path = os.path.join(output_dir, f'frame_{i+1:04d}.jpg')
                    shutil.copy2(frame_path, output_path)
                return True

            # 计算平均光流矢量
            avg_flow = np.mean(flow_vectors, axis=0)
            print(f"📊 Clip A平均运动矢量: ({avg_flow[0]:.2f}, {avg_flow[1]:.2f})")

            # 步骤c: 对后半段序列应用仿射变换
            print(f"🔧 对后半段({len(clip_b_frames)}帧)应用运动校正...")

            corrected_frames = []

            # 保持前半段不变
            for i, (frame_path, img) in enumerate(clip_a_frames):
                output_path = os.path.join(output_dir, f'frame_{i+1:04d}.jpg')
                cv2.imwrite(output_path, img)
                corrected_frames.append(output_path)

            # 校正后半段
            for i, (frame_path, img) in enumerate(clip_b_frames):
                # 计算当前帧相对于目标运动的偏移
                if i < len(clip_b_frames) - 1:
                    gray1 = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
                    gray2 = cv2.cvtColor(clip_b_frames[i+1][1], cv2.COLOR_BGR2GRAY)

                    # 计算当前帧的光流
                    current_flow = cv2.calcOpticalFlowFarneback(gray1, gray2, None, 0.5, 3, 15, 3, 5, 1.2, 0)
                    if current_flow is not None:
                        current_avg_flow_x = np.mean(current_flow[:, :, 0])
                        current_avg_flow_y = np.mean(current_flow[:, :, 1])
                        current_avg_flow = [current_avg_flow_x, current_avg_flow_y]
                    else:
                        current_avg_flow = [0, 0]

                    # 计算需要的校正量
                    correction = np.array(avg_flow) - np.array(current_avg_flow)

                    # 应用微小的仿射变换
                    h, w = img.shape[:2]

                    # 创建仿射变换矩阵（微小的平移）
                    tx, ty = correction * 0.1  # 缩放校正强度

                    # 限制变换幅度，避免过度校正
                    tx = np.clip(tx, -2, 2)
                    ty = np.clip(ty, -2, 2)

                    M = np.float32([[1, 0, tx], [0, 1, ty]])

                    # 应用变换
                    corrected_img = cv2.warpAffine(img, M, (w, h), borderMode=cv2.BORDER_REFLECT)

                    print(f"🔧 帧{len(corrected_frames)+1}: 校正({tx:.1f}, {ty:.1f})")
                else:
                    corrected_img = img

                # 保存校正后的帧
                output_path = os.path.join(output_dir, f'frame_{len(corrected_frames)+1:04d}.jpg')
                cv2.imwrite(output_path, corrected_img)
                corrected_frames.append(output_path)

            print(f"✅ 光流运动匹配完成: {len(frames)} -> {len(corrected_frames)} 帧")
            print(f"🎯 运动趋势已对齐，实现动作连续性")
            return True

        except ImportError:
            print(f"⚠️ OpenCV不可用，使用简化光流模拟")

            # 简化版本：基于帧差的运动分析
            mid_point = len(frames) // 2

            # 分析前后两段的运动强度
            front_motion = 0
            back_motion = 0

            # 简单的帧差分析
            for i in range(mid_point - 1):
                # 这里可以添加简单的像素差分析
                pass

            # 直接复制，但保持顺序
            for i, frame_path in enumerate(frames):
                output_path = os.path.join(output_dir, f'frame_{i+1:04d}.jpg')
                shutil.copy2(frame_path, output_path)

            print(f"✅ 简化光流处理完成: {len(frames)} 帧")
            return True

    except Exception as e:
        print(f"❌ 光流运动匹配失败: {e}")
        # 最后的回退方案
        try:
            import glob
            frames = sorted(glob.glob(os.path.join(input_dir, '*.jpg')))
            for i, frame_path in enumerate(frames):
                output_path = os.path.join(output_dir, f'frame_{i+1:04d}.jpg')
                shutil.copy2(frame_path, output_path)
            return True
        except:
            return False

def apply_time_remapping(input_dir, output_dir, target_frame_count):
    """
    步骤2.3: 时间重映射与融合 (Time Remapping) - 按照技术文档V3.0实现
    目标: 从96fps的超高帧率序列中重新采样出24帧，使用非线性时间曲线
    """
    try:
        print(f"⏱️ 步骤2.3: 时间重映射与融合 (Time Remapping)")
        print(f"🎯 目标: 从超高帧率序列中挑选最合适的{int(target_frame_count)}帧")

        import glob
        import numpy as np

        frames = sorted(glob.glob(os.path.join(input_dir, '*.jpg')))

        if len(frames) == 0:
            print(f"❌ 没有找到输入帧")
            return False

        os.makedirs(output_dir, exist_ok=True)

        target_count = int(target_frame_count)

        print(f"📊 输入: {len(frames)}帧超高帧率序列 -> 输出: {target_count}帧桥接视频")

        # 实现技术文档中的非线性时间曲线
        print(f"📈 创建非线性时间曲线...")

        # 步骤a: 创建非线性的"时间曲线"
        # 这个曲线开始时速度较慢（匹配Clip A的结尾），中间加速，结束时速度再次放缓（匹配Clip B的开头）

        t = np.linspace(0, 1, target_count)

        # V3.1反卡顿优化：使用超平滑的五次贝塞尔曲线
        def ultra_smooth_curve(t):
            """
            五次超平滑缓动函数，消除任何突变
            实现极其平滑的开始慢-中间快-结束慢效果
            """
            # 五次贝塞尔曲线，更平滑的过渡
            return np.where(t < 0.5,
                           16 * t**5,  # 前半段：极其缓慢的加速
                           1 - 16 * (1-t)**5)  # 后半段：极其缓慢的减速

        # 应用超平滑缓动函数
        smooth_t = ultra_smooth_curve(t)

        print(f"📊 时间曲线特性:")
        print(f"   - 开始速度: {(smooth_t[1] - smooth_t[0]) * len(frames):.1f} 帧/步")
        print(f"   - 中间速度: {(smooth_t[target_count//2+1] - smooth_t[target_count//2]) * len(frames):.1f} 帧/步")
        print(f"   - 结束速度: {(smooth_t[-1] - smooth_t[-2]) * len(frames):.1f} 帧/步")

        # 步骤b: 根据时间曲线重新采样
        print(f"🎬 根据时间曲线重新采样...")

        # 映射到帧索引
        frame_indices = smooth_t * (len(frames) - 1)

        # 确保采样的多样性，避免重复
        used_indices = set()
        final_indices = []

        for i, ideal_idx in enumerate(frame_indices):
            # 找到最接近的整数索引
            candidate_idx = int(round(ideal_idx))

            # 如果已经使用过，寻找附近未使用的索引
            if candidate_idx in used_indices:
                # 搜索范围
                search_range = max(1, len(frames) // target_count)
                found = False

                for offset in range(1, search_range + 1):
                    # 优先选择更接近理想位置的索引
                    for direction in [1, -1]:
                        test_idx = candidate_idx + direction * offset
                        if 0 <= test_idx < len(frames) and test_idx not in used_indices:
                            candidate_idx = test_idx
                            found = True
                            break
                    if found:
                        break

                if not found:
                    # 如果还是找不到，使用线性分布
                    candidate_idx = int((i * (len(frames) - 1)) / max(1, target_count - 1))

            # 确保索引有效
            candidate_idx = max(0, min(candidate_idx, len(frames) - 1))
            used_indices.add(candidate_idx)
            final_indices.append(candidate_idx)

        # 步骤c: 导出最终的24帧
        print(f"📤 导出最终桥接序列...")

        for i, source_idx in enumerate(final_indices):
            source_path = frames[source_idx]
            output_path = os.path.join(output_dir, f'frame_{i+1:04d}.jpg')
            shutil.copy2(source_path, output_path)

            # 显示采样信息（仅前几帧和后几帧）
            if i < 3 or i >= target_count - 3:
                relative_pos = source_idx / len(frames)
                print(f"   帧{i+1:2d}: 源帧{source_idx:3d} (位置{relative_pos:.1%})")

        print(f"✅ 时间重映射完成: {len(frames)} -> {target_count} 帧")
        print(f"🎉 成功创建非线性时间过渡，确保速度平滑变化")

        # 验证采样质量
        unique_indices = len(set(final_indices))
        if unique_indices == target_count:
            print(f"✅ 采样质量: 完美 (无重复帧)")
        else:
            print(f"⚠️ 采样质量: {unique_indices}/{target_count} 独特帧")

        return True

    except Exception as e:
        print(f"❌ 时间重映射失败: {e}")
        return False

def complex_seamless_concatenation(video_paths, output_video_path, temp_dir=None):
    """
    复杂的AI插帧无缝拼接（原有逻辑）
    """
    try:
        # 创建临时工作目录
        if temp_dir is None:
            temp_dir = VIDEO_SEAMLESS_CONFIG['temp_dir']

        work_dir = os.path.join(temp_dir, f'seamless_concat_{int(time.time())}')
        os.makedirs(work_dir, exist_ok=True)

        print(f"🎬 开始AI插帧无缝拼接 {len(video_paths)} 个视频")
        print(f"📁 工作目录: {work_dir}")

        # 步骤1: 处理所有接缝
        smoothed_seams = []
        for i in range(len(video_paths) - 1):
            print(f"\n🔗 处理接缝 {i}: {os.path.basename(video_paths[i])} -> {os.path.basename(video_paths[i+1])}")

            seam_video = process_video_seam(video_paths[i], video_paths[i+1], work_dir, i)
            if seam_video:
                smoothed_seams.append(seam_video)
                print(f"✅ 接缝 {i} 处理完成")
            else:
                print(f"❌ 接缝 {i} 处理失败")
                return False

        # 步骤2: 裁剪原始视频片段
        trimmed_clips = []
        for i, video_path in enumerate(video_paths):
            trimmed_path = os.path.join(work_dir, f'trimmed_clip_{i}.mp4')

            # 确定裁剪参数
            trim_start = i > 0  # 第一个视频不裁剪开头
            trim_end = i < len(video_paths) - 1  # 最后一个视频不裁剪结尾

            print(f"✂️ 裁剪视频 {i}: {os.path.basename(video_path)} (开头:{trim_start}, 结尾:{trim_end})")

            success = trim_video_for_seamless(video_path, trimmed_path, trim_start, trim_end)
            if success:
                trimmed_clips.append(trimmed_path)
                print(f"✅ 视频 {i} 裁剪完成")
            else:
                print(f"❌ 视频 {i} 裁剪失败")
                return False

        # 步骤3: 创建最终拼接列表
        final_concat_list = []
        for i in range(len(trimmed_clips)):
            # 添加裁剪后的视频片段
            final_concat_list.append(trimmed_clips[i])

            # 添加平滑接缝（除了最后一个片段）
            if i < len(smoothed_seams):
                final_concat_list.append(smoothed_seams[i])

        print(f"\n📝 拼接列表:")
        for i, video in enumerate(final_concat_list):
            print(f"  {i+1}. {os.path.basename(video)}")

        # 步骤4: 执行最终拼接
        print(f"\n🔗 执行最终拼接...")
        success = concat_videos(final_concat_list, output_video_path)

        if success:
            print(f"✅ AI插帧无缝拼接完成: {output_video_path}")

            # 清理临时文件
            if os.path.exists(work_dir):
                shutil.rmtree(work_dir)
                print(f"🧹 清理临时文件完成")

            return True
        else:
            print(f"❌ 最终拼接失败")
            return False

    except Exception as e:
        print(f"❌ AI插帧无缝拼接失败: {e}")
        return False

def create_seamless_video_from_last_frames(video_paths, output_video_path):
    """
    从视频的尾帧生成新视频，然后进行无缝拼接

    Args:
        video_paths (list): 原始视频路径列表
        output_video_path (str): 输出视频路径

    Returns:
        bool: 是否成功
    """
    try:
        print(f"🎬 从 {len(video_paths)} 个视频的尾帧生成新视频并拼接")

        # 步骤1: 提取所有视频的尾帧
        last_frames = []
        for i, video_path in enumerate(video_paths):
            if not os.path.exists(video_path):
                print(f"❌ 视频文件不存在: {video_path}")
                return False

            # 提取尾帧
            last_frame_path = extract_last_frame(video_path, app.config['OUTPUT_FOLDER'])
            if last_frame_path:
                last_frames.append(last_frame_path)
                print(f"✅ 提取尾帧 {i}: {os.path.basename(last_frame_path)}")
            else:
                print(f"❌ 提取尾帧 {i} 失败")
                return False

        # 步骤2: 从尾帧生成新视频（这里需要调用视频生成API）
        # 注意：这个步骤需要异步处理，因为视频生成需要时间
        print("📝 注意: 从尾帧生成新视频需要调用API，这是一个异步过程")
        print("💡 建议: 先生成所有需要的视频，然后再调用拼接功能")

        # 步骤3: 如果所有视频都已准备好，执行无缝拼接
        return seamless_video_concatenation(video_paths, output_video_path)

    except Exception as e:
        print(f"❌ 从尾帧生成视频并拼接失败: {e}")
        return False

# RunningHub上传图片

def runninghub_upload_image(api_key, image_path):
    """上传图片到RunningHub，增强错误处理"""
    try:
        if not os.path.exists(image_path):
            print(f"❌ 图片文件不存在: {image_path}")
            return None
        
        print(f"🔄 上传图片到RunningHub: {image_path}")
        
        conn = http.client.HTTPSConnection("www.runninghub.cn")
        boundary = 'wL36Yn8afVp8Ag7AmP8qZ0SA4n1v9T'
        dataList = []
        dataList.append(encode('--' + boundary))
        dataList.append(encode('Content-Disposition: form-data; name=apiKey;'))
        dataList.append(encode('Content-Type: text/plain'))
        dataList.append(encode(''))
        dataList.append(encode(api_key))
        filename = os.path.basename(image_path)
        dataList.append(encode('--' + boundary))
        dataList.append(encode(f'Content-Disposition: form-data; name=file; filename={filename}'))
        fileType = mimetypes.guess_type(image_path)[0] or 'application/octet-stream'
        dataList.append(encode(f'Content-Type: {fileType}'))
        dataList.append(encode(''))
        with open(image_path, 'rb') as f:
            dataList.append(f.read())
        dataList.append(encode('--' + boundary))
        dataList.append(encode('Content-Disposition: form-data; name=fileType;'))
        dataList.append(encode('Content-Type: text/plain'))
        dataList.append(encode(''))
        dataList.append(encode("image"))
        dataList.append(encode('--'+boundary+'--'))
        dataList.append(encode(''))
        body = b'\r\n'.join(dataList)
        headers = {
            'Host': 'www.runninghub.cn',
            'Content-type': f'multipart/form-data; boundary={boundary}'
        }
        
        conn.request("POST", "/task/openapi/upload", body, headers)
        res = conn.getresponse()
        data = res.read()
        result = json.loads(data.decode("utf-8"))
        conn.close()
        
        print(f"📊 RunningHub上传响应: {result}")
        
        if result.get("code") == 0:
            filename = result.get("data", {}).get("fileName", "")
            print(f"✅ 图片上传成功: {filename}")
            return filename
        else:
            print(f"❌ 上传失败: {result.get('message', '未知错误')}")
            return None
            
    except Exception as e:
        print(f"❌ RunningHub上传图片异常: {e}")
        import traceback
        traceback.print_exc()
        return None

def runninghub_create_task(api_key, workflow_id, node_info_list):
    try:
        payload = json.dumps({
            "apiKey": api_key,
            "workflowId": workflow_id,
            "nodeInfoList": node_info_list
        })
        headers = {
            'Host': 'www.runninghub.cn',
            'Content-Type': 'application/json'
        }
        conn = http.client.HTTPSConnection("www.runninghub.cn")
        conn.request("POST", "/task/openapi/create", payload, headers)
        res = conn.getresponse()
        data = res.read()
        result = json.loads(data.decode("utf-8"))
        conn.close()
        return result
    except Exception as e:
        print(f"❌ RunningHub创建任务异常: {e}")
        return None

def runninghub_query_status(api_key, task_id):
    try:
        payload = json.dumps({
            "apiKey": api_key,
            "taskId": task_id
        })
        headers = {
            'Host': 'www.runninghub.cn',
            'Content-Type': 'application/json'
        }
        conn = http.client.HTTPSConnection("www.runninghub.cn")
        conn.request("POST", "/task/openapi/status", payload, headers)
        res = conn.getresponse()
        data = res.read()
        result = json.loads(data.decode("utf-8"))
        conn.close()
        return result
    except Exception as e:
        print(f"❌ RunningHub查询状态异常: {e}")
        return None

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'image' not in request.files:
        return jsonify({'error': '没有选择文件'}), 400
    
    file = request.files['image']
    prompt = request.form.get('prompt', '基于上传的图片生成视频')
    duration = int(request.form.get('duration', VIDEO_DURATION))
    
    if file.filename == '':
        return jsonify({'error': '没有选择文件'}), 400
    
    if file and file.filename and allowed_file(file.filename):
        # 生成唯一文件名
        task_id = str(uuid.uuid4())
        filename = secure_filename(file.filename)
        file_extension = filename.rsplit('.', 1)[1].lower()
        saved_filename = f"{task_id}.{file_extension}"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], saved_filename)
        
        # 保存上传的文件
        file.save(file_path)
        
        # 初始化任务状态
        tasks[task_id] = {
            'status': 'uploading',
            'message': '正在上传图片...',
            'created_at': datetime.now().isoformat(),
            'image_path': file_path,
            'prompt': prompt
        }
        
        try:
            # 直接提交视频生成任务（使用base64编码的图片）
            video_task_id = invoke_video_generation(file_path, prompt, duration)
            if not video_task_id:
                tasks[task_id]['status'] = 'failed'
                tasks[task_id]['message'] = '视频生成任务提交失败'
                return jsonify({'error': '视频生成任务提交失败'}), 500
            
            tasks[task_id]['status'] = 'processing'
            tasks[task_id]['message'] = '任务已提交，正在处理...'
            tasks[task_id]['video_task_id'] = video_task_id
            
            return jsonify({
                'task_id': task_id,
                'message': '任务提交成功'
            })
            
        except Exception as e:
            tasks[task_id]['status'] = 'failed'
            tasks[task_id]['message'] = f'处理失败: {str(e)}'
            return jsonify({'error': f'处理失败: {str(e)}'}), 500
    
    return jsonify({'error': '不支持的文件格式'}), 400

@app.route('/run_workflow', methods=['POST'])
def run_workflow():
    # 获取产品名、图片、提示词
    product_name = request.form.get('product')
    prompt = request.form.get('prompt', '请输入提示词')
    file = request.files.get('image')
    # 选择产品图片或用户上传
    if file and file.filename:
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)
    else:
        # 用产品默认图片
        product = next((p for p in PRODUCTS if p['name'] == product_name), None)
        if not product:
            return jsonify({'error': '未找到产品图片'}), 400
        file_path = os.path.join(app.config['PRODUCT_FOLDER'], product['filename'])
    # 上传到RunningHub
    uploaded_filename = runninghub_upload_image(RUNNINGHUB_API_KEY, file_path)
    if not uploaded_filename:
        return jsonify({'error': '图片上传到RunningHub失败'}), 500
    # 构造节点参数
    node_info_list = [
        {"nodeId": "52", "fieldName": "prompt", "fieldValue": prompt},
        {"nodeId": "39", "fieldName": "image", "fieldValue": uploaded_filename},
        {"nodeId": "37", "fieldName": "seed", "fieldValue": int(time.time())}
    ]
    # 创建任务
    create_result = runninghub_create_task(RUNNINGHUB_API_KEY, RUNNINGHUB_WORKFLOW_ID, node_info_list)
    if create_result and create_result.get("code") == 0:
        workflow_task_id = create_result.get("data", {}).get("taskId")
        task_id = str(uuid.uuid4())
        tasks[task_id] = {
            'status': 'processing',
            'message': '任务已提交，正在处理...',
            'workflow_task_id': workflow_task_id,
            'product': product_name,
            'prompt': prompt
        }
        return jsonify({'task_id': task_id, 'message': '任务提交成功'})
    else:
        return jsonify({'error': '创建RunningHub任务失败'}), 500

@app.route('/workflow_status/<task_id>')
def workflow_status(task_id):
    if task_id not in tasks or 'workflow_task_id' not in tasks[task_id]:
        return jsonify({'error': '任务不存在'}), 404
    workflow_task_id = tasks[task_id]['workflow_task_id']
    result = runninghub_query_status(RUNNINGHUB_API_KEY, workflow_task_id)
    # 修正：确保result为dict
    if isinstance(result, str):
        try:
            result = json.loads(result)
        except Exception:
            return jsonify({'error': '查询失败', 'detail': result}), 500
    if result and result.get('code') == 0:
        status = result.get('data', {}).get('status')
        tasks[task_id]['message'] = status
        if status == 'Success':
            images = result.get('data', {}).get('images', [])
            preview_urls = []
            for idx, img_url in enumerate(images):
                img_data = requests.get(img_url).content
                out_path = os.path.join(app.config['OUTPUT_FOLDER'], f'{task_id}_preview_{idx+1}.png')
                with open(out_path, 'wb') as f:
                    f.write(img_data)
                preview_urls.append(f'/outputs/{task_id}_preview_{idx+1}.png')
            tasks[task_id]['status'] = 'completed'
            tasks[task_id]['preview_urls'] = preview_urls
        elif status == 'Fail':
            tasks[task_id]['status'] = 'failed'
        else:
            tasks[task_id]['status'] = 'processing'
        return jsonify(tasks[task_id])
    else:
        return jsonify({'error': '查询失败', 'detail': result}), 500

@app.route('/outputs/<filename>')
def serve_output(filename):
    return send_file(os.path.join(app.config['OUTPUT_FOLDER'], filename))

@app.route('/select_image', methods=['POST'])
def select_image():
    task_id = request.form.get('task_id')
    image_idx = int(request.form.get('image_idx', 0))
    if task_id not in tasks or 'preview_urls' not in tasks[task_id]:
        return jsonify({'error': '任务不存在或未生成图片'}), 404
    selected_url = tasks[task_id]['preview_urls'][image_idx]
    tasks[task_id]['selected_image'] = selected_url
    return jsonify({'selected_image': selected_url})

@app.route('/status/<task_id>')
def get_status(task_id):
    if task_id not in tasks:
        return jsonify({'error': '任务不存在'}), 404
    
    task = tasks[task_id]
    
    # 如果任务正在处理中，查询状态
    if task['status'] == 'processing' and 'video_task_id' in task:
        file_id, status = query_video_generation(task['video_task_id'])
        
        if status == 'Finished':
            # 下载视频
            output_filename = f"{task_id}.mp4"
            video_path = fetch_video_result(file_id, output_filename)
            
            if video_path:
                # 提取视频尾帧
                last_frame_path = extract_last_frame(video_path, app.config['OUTPUT_FOLDER'])

                task['status'] = 'completed'
                task['message'] = '视频生成完成'
                task['video_path'] = video_path
                task['video_url'] = f'/download_video/{task_id}'

                # 保存尾帧路径
                if last_frame_path:
                    task['last_frame_path'] = last_frame_path
                    task['last_frame_url'] = f'/outputs/{os.path.basename(last_frame_path)}'
                    print(f"✅ 视频尾帧提取成功: {last_frame_path}")
                else:
                    print(f"⚠️ 视频尾帧提取失败，但视频生成成功")
            else:
                task['status'] = 'failed'
                task['message'] = '视频下载失败'
        elif status in ['Preparing', 'Queueing', 'Processing']:
            task['message'] = f'正在处理中... ({status})'
        elif status in ['Fail', 'Unknown', 'Error']:
            task['status'] = 'failed'
            task['message'] = f'处理失败: {status}'
    
    return jsonify(task)

@app.route('/download/<task_id>')
def download_video(task_id):
    if task_id not in tasks:
        return jsonify({'error': '任务不存在'}), 404
    task = tasks[task_id]
    if task['status'] != 'completed' or 'video_path' not in task:
        return jsonify({'error': '视频尚未生成完成'}), 400
    # 强制指定 mimetype
    return send_file(task['video_path'], as_attachment=False, mimetype='video/mp4')

@app.route('/download_video/<task_id>')
def download_video_direct(task_id):
    """直接通过MiniMax task_id下载视频，支持Range请求用于视频预览"""
    try:
        # 1. 查询任务状态
        file_id, status = query_video_generation(task_id)
        
        if status != 'Finished' or not file_id:
            return jsonify({'error': f'视频尚未完成，当前状态: {status}'}), 400
        
        # 2. 下载视频
        output_filename = f"{task_id}.mp4"
        video_path = fetch_video_result(file_id, output_filename)
        
        if not video_path:
            return jsonify({'error': '视频下载失败'}), 500
        
        # 3. 支持HTTP Range请求的视频流
        from flask import Response
        
        # 获取文件大小
        file_size = os.path.getsize(video_path)
        
        # 处理Range请求
        range_header = request.headers.get('Range', None)
        if range_header:
            # 解析Range头
            byte_start = 0
            byte_end = file_size - 1
            
            if range_header:
                range_match = re.match(r'bytes=(\d+)-(\d*)', range_header)
                if range_match:
                    byte_start = int(range_match.group(1))
                    if range_match.group(2):
                        byte_end = int(range_match.group(2))
            
            # 读取指定范围的数据
            content_length = byte_end - byte_start + 1
            
            def generate():
                with open(video_path, 'rb') as f:
                    f.seek(byte_start)
                    remaining = content_length
                    while remaining:
                        chunk_size = min(8192, remaining)
                        data = f.read(chunk_size)
                        if not data:
                            break
                        remaining -= len(data)
                        yield data
            
            response = Response(
                generate(),
                206,  # Partial Content
                headers={
                    'Content-Range': f'bytes {byte_start}-{byte_end}/{file_size}',
                    'Accept-Ranges': 'bytes',
                    'Content-Length': str(content_length),
                    'Content-Type': 'video/mp4'
                }
            )
            return response
        else:
            # 完整文件响应
            def generate():
                with open(video_path, 'rb') as f:
                    while True:
                        data = f.read(8192)
                        if not data:
                            break
                        yield data
            
            response = Response(
                generate(),
                headers={
                    'Content-Length': str(file_size),
                    'Content-Disposition': 'inline; filename=video.mp4',
                    'Accept-Ranges': 'bytes',
                    'Content-Type': 'video/mp4'
                }
            )
            return response
        
    except Exception as e:
        return jsonify({'error': f'下载失败: {str(e)}'}), 500

@app.route('/check_video_status/<task_id>')
def check_video_status(task_id):
    """检查视频任务状态"""
    try:
        file_id, status = query_video_generation(task_id)
        return jsonify({
            'task_id': task_id,
            'status': status,
            'file_id': file_id,
            'message': f'任务状态: {status}'
        })
    except Exception as e:
        return jsonify({'error': f'查询失败: {str(e)}'}), 500

@app.route('/products')
def get_products():
    product_list = []
    for p in PRODUCTS:
        product_list.append({
            "name": p["name"],
            "image_url": f"/product_image/{p['filename']}"
        })
    return jsonify(product_list)

@app.route('/product_image/<filename>')
def product_image(filename):
    path = os.path.join(app.config['PRODUCT_FOLDER'], filename)
    return send_file(path)

@app.route('/tasks')
def list_tasks():
    return jsonify(list(tasks.keys()))

# ========== 创意生成文案模块 ==========
@app.route('/creative_directions', methods=['POST'])
def creative_directions():
    data = request.json or {}
    product_name = data.get('product_name', '')
    ingredients = data.get('ingredients', '')
    benefits = data.get('benefits', '')
    usage_scene = data.get('usage_scene', '')

    template_path = os.path.join(os.path.dirname(__file__), '文案')
    if not os.path.exists(template_path):
        return jsonify({'error': '找不到文案模板文件'}), 500

    with open(template_path, 'r', encoding='utf-8') as f:
        prompt_template = f.read()

    prompt = prompt_template.replace("{{PRODUCT_NAME}}", product_name)\
                            .replace("{{INGREDIENTS}}", ingredients)\
                            .replace("{{BENEFITS}}", benefits)\
                            .replace("{{USAGE_SCENE}}", usage_scene)

    if not DEEPSEEK_API_KEY or not DEEPSEEK_API_URL:
        return jsonify({'error': '缺少 DeepSeek API 配置'}), 500

    headers = {
        "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
        "Content-Type": "application/json"
    }

    payload = {
        "model": "deepseek-chat",
        "messages": [
            {
                "role": "system",
                "content": (
                    "你是一位资深的跨境电商短视频创意策划专家。"
                    "请你根据以下产品信息，输出五个不同方向的视频创意结构。"
                    "每个方向请使用JSON数组结构，包含字段：title, narrative_style, emotional_tone, hook_line, scene_summary, visual_description, character, environment。"
                )
            },
            {
                "role": "user",
                "content": prompt
            }
        ],
        "temperature": 1.0,
        "max_tokens": 1500
    }

    try:
        print("🔄 调用DeepSeek API生成创意方向...")
        resp = requests.post(DEEPSEEK_API_URL, headers=headers, json=payload, timeout=30)
        resp.raise_for_status()
        result = resp.json()
        
        # 解析API返回的结果
        content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
        
        # 尝试解析JSON内容
        import re
        json_match = re.search(r'\[.*\]', content, re.DOTALL)
        if json_match:
            try:
                creatives = json.loads(json_match.group())
                return jsonify({'result': creatives, 'source': 'DeepSeek API'})
            except json.JSONDecodeError:
                print("❌ 解析DeepSeek返回的JSON失败")
                pass
        
        print("❌ DeepSeek API调用失败，使用备用数据")
        
    except Exception as e:
        print(f"❌ DeepSeek API调用异常: {e}")
    
    # 备用数据
    print("🔄 使用备用创意数据")
    mock_creatives = [
        {
            "title": "健康生活新开始",
            "narrative_style": "励志故事",
            "emotional_tone": "希望与自信",
            "hook_line": "每天醒来，你选择什么？",
            "scene_summary": "一位30多岁的男性在清晨醒来，展现从疲惫到充满活力的转变",
            "visual_description": "温暖的晨光透过窗帘，特写镜头捕捉主角从床上起身的瞬间，柔和的光线营造希望感",
            "character": "35岁非裔美国男性，中等身材，穿着舒适的居家服装，表情从疲惫转为充满活力",
            "environment": "温馨的卧室，晨光透过半开的窗帘，温暖的色调营造舒适感"
        },
        {
            "title": "运动后的恢复",
            "narrative_style": "生活记录",
            "emotional_tone": "满足与放松",
            "hook_line": "运动后的完美搭档",
            "scene_summary": "主角在健身房锻炼后，使用产品恢复体力",
            "visual_description": "健身房环境，主角坐在休息区，手持产品，展现运动后的满足感",
            "character": "30岁拉美裔男性，肌肉发达，穿着运动服装，表情满足",
            "environment": "现代化健身房，明亮的灯光，整洁的环境"
        },
        {
            "title": "工作压力释放",
            "narrative_style": "对比展示",
            "emotional_tone": "释放与轻松",
            "hook_line": "工作压力大？试试这个",
            "scene_summary": "对比展示主角工作前后的状态变化",
            "visual_description": "办公室环境，主角从紧张工作状态到放松状态的转变",
            "character": "40岁西班牙裔男性，商务装扮，表情从紧张转为放松",
            "environment": "现代化办公室，自然光线充足，整洁的工作环境"
        },
        {
            "title": "家庭时光",
            "narrative_style": "温馨故事",
            "emotional_tone": "温暖与关爱",
            "hook_line": "为家人，选择最好的",
            "scene_summary": "主角与家人在客厅共度美好时光",
            "visual_description": "温馨的客厅环境，主角与家人互动，展现健康活力",
            "character": "35岁墨西哥裔男性，休闲装扮，表情温暖慈爱",
            "environment": "温馨的家庭客厅，温暖的灯光，舒适的家居环境"
        },
        {
            "title": "户外冒险",
            "narrative_style": "冒险记录",
            "emotional_tone": "活力与冒险",
            "hook_line": "冒险需要充沛的体力",
            "scene_summary": "主角在户外活动中展现充沛的体力",
            "visual_description": "户外自然环境，主角进行户外运动，展现活力四射",
            "character": "32岁非裔美国男性，运动装扮，表情充满活力",
            "environment": "美丽的户外环境，自然光线，充满活力的户外场景"
        }
    ]
    return jsonify({'result': mock_creatives, 'note': '使用备用数据'})


# ========== 生成图片模块 ==========
@app.route('/generate_image_task', methods=['POST'])
def generate_image_task():
    """
    图片生成接口：
    - direct_mode=True  用 workflow_id=1942766049638944770（单图模式）
    - direct_mode=False 用 workflow_id=1942531246742507522（双图模式）
    """
    data = request.json or {}
    prompt = data.get('prompt', '')
    seed = data.get('seed', 1)
    direct_mode = data.get('direct_mode', False)
    if direct_mode:
        workflow_id = "1942766049638944770"
    else:
        workflow_id = "1942531246742507522"
    node_info_list = [
        {"nodeId": "52", "fieldName": "prompt", "fieldValue": prompt},
        {"nodeId": "37", "fieldName": "seed", "fieldValue": seed}
    ]
    create_result = runninghub_create_task(RUNNINGHUB_API_KEY, workflow_id, node_info_list)
    if create_result and isinstance(create_result, dict) and create_result.get("code") == 0:
        task_id = create_result.get("data", {}).get("taskId")
        return jsonify({'task_id': task_id})
    else:
        return jsonify({'error': '图片生成任务提交失败', 'detail': create_result}), 500

@app.route('/image_task_progress/<task_id>')
def image_task_progress(task_id):
    """查询图片生成进度，优先返回webhook数据"""
    # 优先返回 webhook 回调数据
    task = tasks.get(task_id)
    if task and task.get('webhook'):
        print(f"[进度查询] 任务 {task_id} 找到webhook数据")
        if task.get('image_url'):
            return jsonify({
                'status': 'completed',
                'image_url': task.get('image_url'),
                'webhook': True
            })
        else:
            return jsonify({
                'status': 'failed',
                'message': 'webhook回调未包含图片URL',
                'webhook': True
            })
    
    # 如果没有webhook数据，轮询 RunningHub 状态
    print(f"[进度查询] 任务 {task_id} 轮询RunningHub状态")
    
    try:
        result = runninghub_query_status(RUNNINGHUB_API_KEY, task_id)
        
        if not result or not isinstance(result, dict):
            print(f"[进度查询] 任务 {task_id} 查询结果无效: {result}")
            return jsonify({'status': 'processing', 'message': '查询中...'})
        
        if result.get('code') != 0:
            error_msg = result.get('msg', '').lower()
            if 'fail' in error_msg or 'error' in error_msg:
                print(f"[进度查询] 任务 {task_id} 失败: {result.get('msg')}")
                return jsonify({'status': 'failed', 'message': result.get('msg', '任务失败')})
            else:
                print(f"[进度查询] 任务 {task_id} 仍在处理: {result.get('msg')}")
                return jsonify({'status': 'processing', 'message': result.get('msg', '处理中...')})
        
        data = result.get('data', {})
        if isinstance(data, str):
            try:
                data = json.loads(data)
            except json.JSONDecodeError:
                print(f"[进度查询] 任务 {task_id} 数据解析失败")
                return jsonify({'status': 'processing', 'message': '解析数据中...'})
        
        status = data.get('taskstatus', '').upper()
        print(f"[进度查询] 任务 {task_id} 状态: {status}")
        
        if status in ['QUEUED', 'RUNNING', 'PROCESSING', 'PREPARING']:
            netwssurl = data.get('netwssurl')
            resp = {'status': 'processing', 'message': f'任务状态: {status}'}
            if netwssurl:
                resp['netwssurl'] = netwssurl
            return jsonify(resp)
            
        elif status == 'SUCCESS':
            images = data.get('images', [])
            if images:
                image_url = images[0]
                print(f"[进度查询] 任务 {task_id} 成功，图片URL: {image_url}")
                
                # 保存到任务记录
                tasks[task_id] = {
                    'status': 'completed',
                    'image_url': image_url,
                    'polling': True,
                    'timestamp': datetime.now().isoformat()
                }
                
                return jsonify({
                    'status': 'completed', 
                    'image_url': image_url,
                    'polling': True
                })
            else:
                print(f"[进度查询] 任务 {task_id} 成功但无图片")
                return jsonify({'status': 'processing', 'message': '生成中，等待图片...'})
                
        elif status in ['FAIL', 'FAILED', 'ERROR']:
            print(f"[进度查询] 任务 {task_id} 失败")
            return jsonify({'status': 'failed', 'message': f'任务失败: {status}'})
            
        else:
            print(f"[进度查询] 任务 {task_id} 未知状态: {status}")
            return jsonify({'status': 'processing', 'message': f'未知状态: {status}'})
            
    except Exception as e:
        print(f"[进度查询] 任务 {task_id} 异常: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'status': 'error', 'message': f'查询异常: {str(e)}'})

# ========== 视频生成模块 ==========
@app.route('/generate_video_task', methods=['POST'])
def generate_video_task():
    data = request.json or {}
    image_path = data.get('image_path', '')
    prompt = data.get('prompt', '基于上传的图片生成视频')
    duration = int(data.get('duration', VIDEO_DURATION))
    print(f"[生成视频] /generate_video_task 收到请求: image_path={image_path[:60]}..., prompt={prompt}, duration={duration}")
    # 新增：支持 base64 图片字符串
    import re, tempfile, base64, os
    if image_path and image_path.startswith('data:image/'):
        # 解析 base64
        match = re.match(r'data:image/(\w+);base64,(.+)', image_path)
        if match:
            ext, b64data = match.groups()
            with tempfile.NamedTemporaryFile(delete=False, suffix='.'+ext) as tmpf:
                tmpf.write(base64.b64decode(b64data))
                image_path = tmpf.name
        else:
            return jsonify({'error': '图片base64格式无效'}), 400
    if not image_path or not os.path.exists(image_path):
        return jsonify({'error': '图片路径无效'}), 400
    # 修改invoke_video_generation调用，传递duration参数
    video_task_id = invoke_video_generation(image_path, prompt, duration)
    if not video_task_id:
        return jsonify({'error': '视频生成任务提交失败'}), 500
    return jsonify({'task_id': video_task_id})

@app.route('/video_task_progress/<task_id>')
def video_task_progress(task_id):
    """查询视频生成进度"""
    print(f"🔍 查询视频进度，任务ID: {task_id}")
    try:
        file_id, status = query_video_generation(task_id)
        print(f"📊 查询结果 - file_id: {file_id}, status: {status}")
        
        # 根据文档返回详细状态信息
        if status == 'Finished':
            result = {
                'status': 'completed', 
                'file_id': file_id, 
                'message': '视频生成成功，可下载',
                'download_url': f'/download_video/{task_id}' if file_id else None
            }
            print(f"✅ 视频完成: {result}")
            return jsonify(result)
        elif status == 'Preparing':
            result = {'status': 'preparing', 'message': '准备中...'}
            print(f"⏳ 准备中: {result}")
            return jsonify(result)
        elif status == 'Queueing':
            result = {'status': 'queueing', 'message': '队列中...'}
            print(f"⏳ 队列中: {result}")
            return jsonify(result)
        elif status == 'Processing':
            result = {'status': 'processing', 'message': '生成中...'}
            print(f"⏳ 生成中: {result}")
            return jsonify(result)
        elif status in ['Fail', 'Unknown', 'Error']:
            result = {'status': 'failed', 'message': '生成失败'}
            print(f"❌ 生成失败: {result}")
            return jsonify(result)
        else:
            result = {'status': status, 'message': f'未知状态: {status}'}
            print(f"❓ 未知状态: {result}")
            return jsonify(result)
    except Exception as e:
        print(f"❌ 查询异常: {e}")
        return jsonify({'status': 'error', 'message': f'查询失败: {str(e)}'})

@app.route('/generate_image_task_test', methods=['POST'])
def generate_image_task_test():
    try:
        # 原有代码缩进
        data = request.json or {}
        prompt = data.get('prompt', '')
        seed = data.get('seed', 1)
        image = data.get('image', '')  # RunningHub 已上传文件名
        image_file = data.get('image_file', '')  # 本地图片 base64 或文件名
        uploaded_filename = image
        # 如果没传 image，优先用 image_file 上传
        if not uploaded_filename and image_file:
            import base64
            import tempfile
            import os
            # 判断 image_file 是 base64 还是本地路径
            if image_file.startswith('data:image/'):
                # base64
                header, b64data = image_file.split(',', 1)
                suffix = header.split('/')[1].split(';')[0]
                with tempfile.NamedTemporaryFile(delete=False, suffix='.'+suffix) as tmpf:
                    tmpf.write(base64.b64decode(b64data))
                    tmp_path = tmpf.name
            else:
                # 修正：如果是相对路径（如 cp/xxx.png），补全为绝对路径
                if not os.path.isabs(image_file):
                    tmp_path = os.path.join(os.path.dirname(__file__), image_file)
                else:
                    tmp_path = image_file
            uploaded_filename = runninghub_upload_image(RUNNINGHUB_API_KEY, tmp_path)
            # 清理临时文件
            if image_file.startswith('data:image/') and os.path.exists(tmp_path):
                os.remove(tmp_path)
            if not uploaded_filename:
                return jsonify({'error': '图片上传到RunningHub失败'}), 500
        workflow_id = "1942785263645208577"
        # 使用 ngrok 公网地址作为 webhookUrl
        webhook_url = f"{NGROK_BASE_URL}/image_task_webhook"
        node_info_list = [
            {"nodeId": "52", "fieldName": "prompt", "fieldValue": prompt},
            {"nodeId": "39", "fieldName": "image", "fieldValue": uploaded_filename},
            {"nodeId": "37", "fieldName": "seed", "fieldValue": seed}
        ]
        # 按官方案例，webhookUrl 作为顶层参数传递
        payload = {
            "apiKey": RUNNINGHUB_API_KEY,
            "workflowId": workflow_id,
            "nodeInfoList": node_info_list,
            "webhookUrl": webhook_url
        }
        import http.client, json
        conn = http.client.HTTPSConnection("www.runninghub.cn")
        headers = {
            'Host': 'www.runninghub.cn',
            'Content-Type': 'application/json'
        }
        conn.request("POST", "/task/openapi/create", json.dumps(payload), headers)
        res = conn.getresponse()
        data = res.read()
        result = json.loads(data.decode("utf-8"))
        conn.close()
        if result and isinstance(result, dict) and result.get("code") == 0:
            task_id = result.get("data", {}).get("taskId")
            return jsonify({'task_id': task_id})
        else:
            return jsonify({'error': '图片生成任务提交失败', 'detail': result}), 500
    except Exception as e:
        import traceback
        print('图片生成任务异常:', e)
        traceback.print_exc()
        return jsonify({'error': f'图片生成任务异常: {str(e)}', 'trace': traceback.format_exc()}), 500

@app.route('/generate_video_from_url', methods=['POST'])
def generate_video_from_url():
    data = request.json or {}
    image_url = data.get('image_url', '')
    prompt = data.get('prompt') or "人物生动的讲解他的手中的产品，（产品里面不要变成液体；产品里面是药片不要打开；人物除了看产品或看镜头不要看其他地方；产品展示过程尽量只展示产品的正面；人物必须开口讲解）"
    video_count = int(data.get('video_count', 1))
    duration = int(data.get('duration', VIDEO_DURATION)) if 'duration' in data else VIDEO_DURATION
    print(f"[生成视频] /generate_video_from_url 收到请求: image_url={image_url}, prompt={prompt}, video_count={video_count}, duration={duration}")
    if not image_url:
        return jsonify({'error': '未提供图片URL'}), 400
    try:
        # 下载图片到本地
        ext = image_url.split('.')[-1].split('?')[0]
        if len(ext) > 5 or '/' in ext:
            ext = 'png'
        filename = f"{uuid.uuid4()}.{ext}"
        local_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        resp = requests.get(image_url)
        with open(local_path, 'wb') as f:
            f.write(resp.content)
        # 批量生成视频
        task_ids = []
        for _ in range(video_count):
            video_task_id = invoke_video_generation(local_path, prompt)
            if not video_task_id:
                return jsonify({'error': '视频生成任务提交失败'}), 500
            task_ids.append(video_task_id)
        return jsonify({'task_ids': task_ids})
    except Exception as e:
        return jsonify({'error': f'图片下载或视频生成失败: {str(e)}'}), 500

@app.route('/generate_video_from_text', methods=['POST'])
def generate_video_from_text():
    """根据纯文本提示词生成视频"""
    data = request.json or {}
    prompt = data.get('prompt', '')
    model = data.get('model', VIDEO_CONFIG['model'])
    duration = data.get('duration', VIDEO_CONFIG['duration'])
    resolution = data.get('resolution', VIDEO_CONFIG['resolution'])
    print(f"[生成视频] /generate_video_from_text 收到请求: prompt={prompt}, model={model}, duration={duration}, resolution={resolution}")
    
    if not prompt:
        return jsonify({'error': '请提供提示词'}), 400
    
    try:
        # 直接调用视频生成API（不传图片）
        video_task_id = invoke_video_generation(prompt=prompt)
        if not video_task_id:
            return jsonify({'error': '视频生成任务提交失败'}), 500
        
        return jsonify({'task_id': video_task_id})
    except Exception as e:
        return jsonify({'error': f'视频生成失败: {str(e)}'}), 500

# 新增：RunningHub webhook 回调接口
@app.route('/image_task_webhook', methods=['POST'])
def image_task_webhook():
    """接收RunningHub的webhook回调"""
    data = request.json or {}
    print(f"[webhook] 收到图片任务完成回调: {data}")
    
    task_id = data.get('taskId')
    if not task_id:
        print("❌ webhook回调缺少taskId")
        return jsonify({'status': 'error', 'message': '缺少taskId'}), 400
    
    event_data = data.get('eventData')
    parsed_event = None
    image_url = None
    
    try:
        # eventData 可能是 str 也可能是 dict
        if isinstance(event_data, str):
            parsed_event = json.loads(event_data)
        elif isinstance(event_data, dict):
            parsed_event = event_data
        else:
            parsed_event = {}
            
        print(f"[webhook] 解析的事件数据: {parsed_event}")
        
        # 尝试获取图片URL
        if parsed_event and 'data' in parsed_event and parsed_event['data']:
            if isinstance(parsed_event['data'], list) and len(parsed_event['data']) > 0:
                # 取第一个图片
                first_data = parsed_event['data'][0]
                if isinstance(first_data, dict):
                    image_url = first_data.get('fileUrl') or first_data.get('url')
                elif isinstance(first_data, str):
                    image_url = first_data
            elif isinstance(parsed_event['data'], dict):
                image_url = parsed_event['data'].get('fileUrl') or parsed_event['data'].get('url')
            elif isinstance(parsed_event['data'], str):
                image_url = parsed_event['data']
                
        # 如果没有找到图片URL，尝试其他字段
        if not image_url:
            image_url = parsed_event.get('fileUrl') or parsed_event.get('url')
            
        print(f"[webhook] 提取到的图片URL: {image_url}")
        
    except Exception as e:
        print(f'❌ webhook eventData 解析失败: {e}')
        import traceback
        traceback.print_exc()
    
    # 保存任务结果
    tasks[task_id] = {
        'webhook': True,
        'webhook_data': data,
        'image_url': image_url,
        'event_data': parsed_event,
        'status': 'completed' if image_url else 'failed',
        'timestamp': datetime.now().isoformat()
    }
    
    print(f"[webhook] 任务 {task_id} 处理完成，状态: {'成功' if image_url else '失败'}")
    
    return jsonify({
        'status': 'ok', 
        'task_id': task_id,
        'image_url': image_url,
        'message': '回调处理成功'
    })

@app.route('/creative_directions_prompt', methods=['POST'])
def creative_directions_prompt():
    """
    输入: visual_description, character, environment, product_color, emotional_tone, hook_line
    输出: prompt_zh, prompt_en
    """
    data = request.json or {}
    visual_description = data.get('visual_description', '')
    character = data.get('character', '')
    environment = data.get('environment', '')
    product_color = data.get('product_color', '蓝色')
    emotional_tone = data.get('emotional_tone', '中性')
    hook_line = data.get('hook_line', '')

    # 拼接中文提示词
    prompt_zh = f"{visual_description}，{character}，{environment}，产品颜色：{product_color}，情感基调：{emotional_tone}，钩子语：{hook_line}".strip('，, ')

    # 生成英文自然语言段落
    meta_instruction = (
        "You are a visual storytelling expert. Please write a single-paragraph cinematic image description based on the information below. "
        "This should resemble a camera direction for a photo-realistic AI image generation. "
        "Include character pose, facial emotion changes, product placement, setting, lighting, and emotional tone. Use natural fluent English, 100–150 words.\n"
        "Do not return bullet points. No explanations. Just the paragraph."
    )
    ad_direction = {
        "character": character,
        "environment": environment,
        "product_color": product_color,
        "visual_description": visual_description,
        "emotional_tone": emotional_tone,
        "hook_line": hook_line
    }
    try:
        headers = {
            "Authorization": f"Bearer {DEEPSEEK_API_KEY}",
            "Content-Type": "application/json"
        }
        payload = {
            "model": "deepseek-chat",
            "messages": [
                {"role": "system", "content": meta_instruction},
                {"role": "user", "content": f"Here is the creative direction:\n{json.dumps(ad_direction, ensure_ascii=False, indent=2)}"}
            ],
            "temperature": 0.7,
            "max_tokens": 500
        }
        resp = requests.post(DEEPSEEK_API_URL, headers=headers, data=json.dumps(payload), timeout=30)
        resp.raise_for_status()
        result = resp.json()
        prompt_en = result["choices"][0]["message"]["content"].strip()
    except Exception as e:
        print('DeepSeek生成失败:', e)
        prompt_en = ''

    return jsonify({
        'prompt_zh': prompt_zh,
        'prompt_en': prompt_en
    })

# ========== 视频上传API ==========

@app.route('/upload_video', methods=['POST'])
def upload_video():
    """上传视频文件"""
    try:
        if 'video' not in request.files:
            return jsonify({'error': '没有选择视频文件'}), 400

        file = request.files['video']
        if file.filename == '':
            return jsonify({'error': '没有选择视频文件'}), 400

        # 检查文件类型
        allowed_extensions = {'mp4', 'avi', 'mov', 'mkv'}
        file_ext = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''

        if file_ext not in allowed_extensions:
            return jsonify({'error': f'不支持的文件格式: {file_ext}'}), 400

        # 生成唯一的文件名
        video_id = str(int(time.time() * 1000))  # 使用时间戳作为ID
        filename = f'uploaded_{video_id}.mp4'

        # 保存文件
        output_path = os.path.join(app.config['OUTPUT_FOLDER'], filename)

        # 如果不是mp4格式，需要转换
        if file_ext != 'mp4':
            # 先保存原文件
            temp_path = os.path.join(app.config['OUTPUT_FOLDER'], f'temp_{video_id}.{file_ext}')
            file.save(temp_path)

            # 使用FFmpeg转换为mp4
            cmd = [
                'ffmpeg', '-y',
                '-i', temp_path,
                '-c:v', 'libx264',
                '-c:a', 'aac',
                '-preset', 'fast',
                '-crf', '23',
                output_path
            ]

            print(f"🔄 转换视频格式: {file_ext} -> mp4")
            result = subprocess.run(cmd, capture_output=True, text=True)

            # 删除临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)

            if result.returncode != 0:
                print(f"❌ 视频转换失败: {result.stderr}")
                return jsonify({'error': '视频格式转换失败'}), 500
        else:
            # 直接保存mp4文件
            file.save(output_path)

        print(f"✅ 视频上传成功: {filename}")

        return jsonify({
            'success': True,
            'message': '视频上传成功',
            'filename': filename,
            'video_id': video_id,
            'video_url': f'/outputs/{filename}',
            'file_size': os.path.getsize(output_path)
        })

    except Exception as e:
        print(f"❌ 视频上传失败: {e}")
        return jsonify({'error': f'上传失败: {str(e)}'}), 500

# ========== 视频拼接API ==========

@app.route('/seamless_concat', methods=['POST'])
def seamless_concat():
    """无缝拼接视频API"""
    try:
        data = request.json or {}
        video_ids = data.get('video_ids', [])
        mode = data.get('mode', 'simple')  # 'simple' 或 'ai'

        if len(video_ids) < 2:
            return jsonify({'error': '至少需要选择2个视频进行拼接'}), 400

        # 构建视频文件路径
        video_paths = []
        for video_id in video_ids:
            video_path = os.path.join(app.config['OUTPUT_FOLDER'], f'{video_id}.mp4')
            if os.path.exists(video_path):
                video_paths.append(video_path)
            else:
                return jsonify({'error': f'视频文件不存在: {video_id}'}), 400

        # 生成输出文件名
        mode_suffix = 'ai' if mode == 'ai' else 'fast'
        output_filename = f'seamless_concat_{mode_suffix}_{int(time.time())}.mp4'
        output_path = os.path.join(app.config['OUTPUT_FOLDER'], output_filename)

        print(f"🎬 开始{mode_suffix}无缝拼接: {len(video_paths)} 个视频")

        # 根据模式设置配置
        if mode == 'ai':
            # 启用AI插帧模式
            VIDEO_SEAMLESS_CONFIG['enable_ai_interpolation'] = True
            VIDEO_SEAMLESS_CONFIG['enable_ultimate_mode'] = False
            print("🤖 使用AI插帧模式（高质量但较慢）")
        elif mode == 'ultimate':
            # 启用终极模式
            VIDEO_SEAMLESS_CONFIG['enable_ai_interpolation'] = False
            VIDEO_SEAMLESS_CONFIG['enable_ultimate_mode'] = True
            print("🚀 使用终极无缝拼接模式 V3.0（追求绝对无缝）")
        else:
            # 使用快速模式
            VIDEO_SEAMLESS_CONFIG['enable_ai_interpolation'] = False
            VIDEO_SEAMLESS_CONFIG['enable_ultimate_mode'] = False
            print("⚡ 使用快速拼接模式（推荐）")

        # 执行无缝拼接
        success = seamless_video_concatenation(video_paths, output_path)

        if success:
            return jsonify({
                'success': True,
                'message': '视频拼接成功',
                'output_video': output_filename,
                'download_url': f'/outputs/{output_filename}'
            })
        else:
            return jsonify({'error': '视频拼接失败'}), 500

    except Exception as e:
        print(f"❌ 拼接API异常: {e}")
        return jsonify({'error': f'拼接失败: {str(e)}'}), 500

@app.route('/get_available_videos')
def get_available_videos():
    """获取可用于拼接的视频列表"""
    try:
        videos = []
        output_dir = app.config['OUTPUT_FOLDER']

        # 扫描输出目录中的视频文件
        for filename in os.listdir(output_dir):
            if filename.endswith('.mp4'):
                video_path = os.path.join(output_dir, filename)
                video_id = filename.replace('.mp4', '')

                # 检查是否有对应的尾帧
                last_frame_path = os.path.join(output_dir, f'{video_id}_last_frame.jpg')
                has_last_frame = os.path.exists(last_frame_path)

                # 检查是否有缩略图，如果没有则生成
                thumbnail_path = os.path.join(output_dir, f'{video_id}_thumbnail.jpg')
                if not os.path.exists(thumbnail_path):
                    extract_video_thumbnail(video_path, output_dir)
                has_thumbnail = os.path.exists(thumbnail_path)

                # 获取视频信息
                video_info = get_video_info(video_path)

                # 格式化文件大小
                file_size = os.path.getsize(video_path)
                size_mb = round(file_size / (1024 * 1024), 2)

                videos.append({
                    'id': video_id,
                    'filename': filename,
                    'has_last_frame': has_last_frame,
                    'last_frame_url': f'/outputs/{video_id}_last_frame.jpg' if has_last_frame else None,
                    'has_thumbnail': has_thumbnail,
                    'thumbnail_url': f'/outputs/{video_id}_thumbnail.jpg' if has_thumbnail else None,
                    'video_url': f'/outputs/{filename}',
                    'size': file_size,
                    'size_mb': size_mb,
                    'video_info': video_info
                })

        # 按文件名排序
        videos.sort(key=lambda x: x['filename'])

        return jsonify({
            'videos': videos,
            'count': len(videos)
        })

    except Exception as e:
        print(f"❌ 获取视频列表异常: {e}")
        return jsonify({'error': f'获取视频列表失败: {str(e)}'}), 500

@app.route('/extract_last_frame', methods=['POST'])
def extract_last_frame_api():
    """提取视频的最后一帧API"""
    try:
        data = request.json or {}
        video_id = data.get('video_id', '')

        if not video_id:
            return jsonify({'error': '请提供视频ID'}), 400

        # 检查视频文件是否存在
        video_path = os.path.join(app.config['OUTPUT_FOLDER'], f'{video_id}.mp4')
        if not os.path.exists(video_path):
            return jsonify({'error': f'视频文件不存在: {video_id}.mp4'}), 400

        # 提取尾帧
        last_frame_path = extract_last_frame(video_path, app.config['OUTPUT_FOLDER'])
        if not last_frame_path:
            return jsonify({'error': '尾帧提取失败'}), 500

        # 返回尾帧信息
        return jsonify({
            'success': True,
            'video_id': video_id,
            'last_frame_path': last_frame_path,
            'last_frame_url': f'/outputs/{os.path.basename(last_frame_path)}',
            'message': '尾帧提取成功'
        })

    except Exception as e:
        print(f"❌ 尾帧提取API异常: {e}")
        return jsonify({'error': f'尾帧提取失败: {str(e)}'}), 500

@app.route('/generate_from_last_frame', methods=['POST'])
def generate_from_last_frame():
    """从尾帧生成新视频"""
    try:
        data = request.json or {}
        video_id = data.get('video_id', '')
        # 使用固定的视频生成提示词
        prompt = FIXED_VIDEO_PROMPT
        duration = int(data.get('duration', VIDEO_DURATION))

        if not video_id:
            return jsonify({'error': '请提供视频ID'}), 400

        # 检查尾帧文件是否存在
        last_frame_path = os.path.join(app.config['OUTPUT_FOLDER'], f'{video_id}_last_frame.jpg')
        if not os.path.exists(last_frame_path):
            return jsonify({'error': '尾帧文件不存在'}), 400

        # 调用视频生成API
        video_task_id = invoke_video_generation(last_frame_path, prompt, duration)
        if not video_task_id:
            return jsonify({'error': '视频生成任务提交失败'}), 500

        # 创建任务记录
        task_id = str(uuid.uuid4())
        tasks[task_id] = {
            'status': 'processing',
            'message': '基于尾帧生成视频中...',
            'created_at': datetime.now().isoformat(),
            'source_video_id': video_id,
            'last_frame_path': last_frame_path,
            'prompt': prompt,
            'video_task_id': video_task_id,
            'type': 'last_frame_generation'
        }

        return jsonify({
            'task_id': task_id,
            'video_task_id': video_task_id,
            'message': '基于尾帧的视频生成任务已提交'
        })

    except Exception as e:
        print(f"❌ 尾帧生成视频API异常: {e}")
        return jsonify({'error': f'生成失败: {str(e)}'}), 500

@app.route('/extend_video', methods=['POST'])
def extend_video():
    """延长视频生成（提取尾帧→生成新视频→无缝拼接）"""
    try:
        data = request.json or {}
        video_id = data.get('video_id', '')
        # 使用固定的视频生成提示词，不再需要用户输入
        prompt = FIXED_VIDEO_PROMPT
        duration = int(data.get('duration', VIDEO_DURATION))

        if not video_id:
            return jsonify({'error': '请提供视频ID'}), 400

        # 检查原视频文件是否存在
        original_video_path = os.path.join(app.config['OUTPUT_FOLDER'], f'{video_id}.mp4')
        if not os.path.exists(original_video_path):
            return jsonify({'error': f'原视频文件不存在: {video_id}.mp4'}), 400

        # 创建延长任务记录
        extend_task_id = str(uuid.uuid4())
        tasks[extend_task_id] = {
            'status': 'processing',
            'message': '开始延长生成...',
            'created_at': datetime.now().isoformat(),
            'original_video_id': video_id,
            'prompt': prompt,
            'duration': duration,
            'type': 'video_extension',
            'steps': {
                'extract_frame': {'status': 'pending', 'message': '等待提取尾帧'},
                'generate_video': {'status': 'pending', 'message': '等待生成新视频'},
                'seamless_concat': {'status': 'pending', 'message': '等待无缝拼接'}
            }
        }

        print(f"🎬 开始延长视频: {video_id}")

        # 步骤1: 提取尾帧
        tasks[extend_task_id]['steps']['extract_frame']['status'] = 'processing'
        tasks[extend_task_id]['steps']['extract_frame']['message'] = '正在提取尾帧...'
        tasks[extend_task_id]['message'] = '正在提取尾帧...'

        last_frame_path = extract_last_frame(original_video_path, app.config['OUTPUT_FOLDER'])
        if not last_frame_path:
            tasks[extend_task_id]['status'] = 'failed'
            tasks[extend_task_id]['message'] = '尾帧提取失败'
            tasks[extend_task_id]['steps']['extract_frame']['status'] = 'failed'
            tasks[extend_task_id]['steps']['extract_frame']['message'] = '尾帧提取失败'
            return jsonify({'error': '尾帧提取失败'}), 500

        tasks[extend_task_id]['steps']['extract_frame']['status'] = 'completed'
        tasks[extend_task_id]['steps']['extract_frame']['message'] = '尾帧提取成功'
        tasks[extend_task_id]['last_frame_path'] = last_frame_path

        # 步骤2: 生成新视频
        tasks[extend_task_id]['steps']['generate_video']['status'] = 'processing'
        tasks[extend_task_id]['steps']['generate_video']['message'] = '正在生成新视频...'
        tasks[extend_task_id]['message'] = '正在生成新视频...'

        video_task_id = invoke_video_generation(last_frame_path, prompt, duration)
        if not video_task_id:
            tasks[extend_task_id]['status'] = 'failed'
            tasks[extend_task_id]['message'] = '视频生成任务提交失败'
            tasks[extend_task_id]['steps']['generate_video']['status'] = 'failed'
            tasks[extend_task_id]['steps']['generate_video']['message'] = '视频生成任务提交失败'
            return jsonify({'error': '视频生成任务提交失败'}), 500

        tasks[extend_task_id]['video_task_id'] = video_task_id
        tasks[extend_task_id]['steps']['generate_video']['video_task_id'] = video_task_id

        # 保存任务到文件
        save_tasks()

        return jsonify({
            'extend_task_id': extend_task_id,
            'video_task_id': video_task_id,
            'message': '延长生成任务已启动',
            'steps': tasks[extend_task_id]['steps']
        })

    except Exception as e:
        print(f"❌ 延长视频API异常: {e}")
        return jsonify({'error': f'延长生成失败: {str(e)}'}), 500

@app.route('/extend_status/<extend_task_id>')
def extend_status(extend_task_id):
    """查询延长生成任务状态"""
    try:
        if extend_task_id not in tasks:
            return jsonify({'error': '任务不存在'}), 404

        task = tasks[extend_task_id]

        # 如果是延长任务且有视频生成任务ID，检查视频生成状态
        if task.get('type') in ['video_extension', 'upload_video_extension'] and 'video_task_id' in task:
            video_task_id = task['video_task_id']

            # 检查视频生成状态
            if task['steps']['generate_video']['status'] == 'processing':
                file_id, status = query_video_generation(video_task_id)
                video_status = {'status': status, 'file_id': file_id}

                if video_status and video_status.get('status') == 'Finished':
                    # 视频生成完成，下载视频
                    file_id = video_status.get('file_id')
                    if file_id:
                        new_video_path = download_video_file(file_id, video_task_id)
                        if new_video_path:
                            task['steps']['generate_video']['status'] = 'completed'
                            task['steps']['generate_video']['message'] = '新视频生成完成'
                            task['new_video_path'] = new_video_path
                            task['new_video_id'] = video_task_id

                            # 开始无缝拼接
                            task['steps']['seamless_concat']['status'] = 'processing'
                            task['steps']['seamless_concat']['message'] = '正在进行无缝拼接...'
                            task['message'] = '正在进行无缝拼接...'

                            # 执行无缝拼接
                            if task.get('type') == 'upload_video_extension':
                                # 上传视频延长
                                original_video_path = task['uploaded_video_path']
                                output_filename = f'extended_upload_{task["uploaded_video_id"]}_{int(time.time())}.mp4'
                            else:
                                # 普通视频延长
                                original_video_path = os.path.join(app.config['OUTPUT_FOLDER'], f"{task['original_video_id']}.mp4")
                                output_filename = f'extended_{task["original_video_id"]}_{int(time.time())}.mp4'

                            video_paths = [original_video_path, new_video_path]
                            output_path = os.path.join(app.config['OUTPUT_FOLDER'], output_filename)

                            concat_success = seamless_video_concatenation(video_paths, output_path)

                            if concat_success:
                                task['steps']['seamless_concat']['status'] = 'completed'
                                task['steps']['seamless_concat']['message'] = '无缝拼接完成'
                                task['status'] = 'completed'
                                task['message'] = '延长生成完成'
                                task['final_video_path'] = output_path
                                task['final_video_url'] = f'/outputs/{output_filename}'

                                # 提取最终视频的尾帧
                                final_last_frame = extract_last_frame(output_path, app.config['OUTPUT_FOLDER'])
                                if final_last_frame:
                                    task['final_last_frame_path'] = final_last_frame
                                    task['final_last_frame_url'] = f'/outputs/{os.path.basename(final_last_frame)}'

                                # 保存任务状态
                                save_tasks()
                            else:
                                task['steps']['seamless_concat']['status'] = 'failed'
                                task['steps']['seamless_concat']['message'] = '无缝拼接失败'
                                task['status'] = 'failed'
                                task['message'] = '无缝拼接失败'
                        else:
                            task['steps']['generate_video']['status'] = 'failed'
                            task['steps']['generate_video']['message'] = '视频下载失败'
                            task['status'] = 'failed'
                            task['message'] = '视频下载失败'
                elif video_status and video_status.get('status') in ['Fail', 'Error']:
                    task['steps']['generate_video']['status'] = 'failed'
                    task['steps']['generate_video']['message'] = '视频生成失败'
                    task['status'] = 'failed'
                    task['message'] = '视频生成失败'

        return jsonify(task)

    except Exception as e:
        print(f"❌ 查询延长状态异常: {e}")
        return jsonify({'error': f'查询失败: {str(e)}'}), 500

@app.route('/upload_video_extend', methods=['POST'])
def upload_video_extend():
    """上传视频并自动延长（提取尾帧→生成新视频→无缝拼接）"""
    try:
        if 'video' not in request.files:
            return jsonify({'error': '请选择视频文件'}), 400

        video_file = request.files['video']
        if video_file.filename == '':
            return jsonify({'error': '请选择视频文件'}), 400

        # 使用固定的视频生成提示词，不再需要用户输入
        prompt = FIXED_VIDEO_PROMPT
        duration = int(request.form.get('duration', VIDEO_DURATION))

        # 保存上传的视频文件
        upload_video_id = str(uuid.uuid4())
        video_filename = f'uploaded_{upload_video_id}.mp4'
        upload_video_path = os.path.join(app.config['OUTPUT_FOLDER'], video_filename)
        video_file.save(upload_video_path)

        print(f"🎬 上传视频延长: {video_filename}")

        # 创建上传延长任务记录
        extend_task_id = str(uuid.uuid4())
        tasks[extend_task_id] = {
            'status': 'processing',
            'message': '开始上传视频延长...',
            'created_at': datetime.now().isoformat(),
            'uploaded_video_id': upload_video_id,
            'uploaded_video_path': upload_video_path,
            'prompt': prompt,
            'duration': duration,
            'type': 'upload_video_extension',
            'steps': {
                'extract_frame': {'status': 'pending', 'message': '等待提取尾帧'},
                'generate_video': {'status': 'pending', 'message': '等待生成新视频'},
                'seamless_concat': {'status': 'pending', 'message': '等待无缝拼接'}
            }
        }

        # 步骤1: 提取尾帧
        tasks[extend_task_id]['steps']['extract_frame']['status'] = 'processing'
        tasks[extend_task_id]['steps']['extract_frame']['message'] = '正在提取尾帧...'
        tasks[extend_task_id]['message'] = '正在提取尾帧...'

        last_frame_path = extract_last_frame(upload_video_path, app.config['OUTPUT_FOLDER'])
        if not last_frame_path:
            tasks[extend_task_id]['status'] = 'failed'
            tasks[extend_task_id]['message'] = '尾帧提取失败'
            tasks[extend_task_id]['steps']['extract_frame']['status'] = 'failed'
            tasks[extend_task_id]['steps']['extract_frame']['message'] = '尾帧提取失败'
            return jsonify({'error': '尾帧提取失败'}), 500

        tasks[extend_task_id]['steps']['extract_frame']['status'] = 'completed'
        tasks[extend_task_id]['steps']['extract_frame']['message'] = '尾帧提取成功'
        tasks[extend_task_id]['last_frame_path'] = last_frame_path

        # 步骤2: 生成新视频
        tasks[extend_task_id]['steps']['generate_video']['status'] = 'processing'
        tasks[extend_task_id]['steps']['generate_video']['message'] = '正在生成新视频...'
        tasks[extend_task_id]['message'] = '正在生成新视频...'

        video_task_id = invoke_video_generation(last_frame_path, prompt, duration)
        if not video_task_id:
            tasks[extend_task_id]['status'] = 'failed'
            tasks[extend_task_id]['message'] = '视频生成任务提交失败'
            tasks[extend_task_id]['steps']['generate_video']['status'] = 'failed'
            tasks[extend_task_id]['steps']['generate_video']['message'] = '视频生成任务提交失败'
            return jsonify({'error': '视频生成任务提交失败'}), 500

        tasks[extend_task_id]['video_task_id'] = video_task_id
        tasks[extend_task_id]['steps']['generate_video']['video_task_id'] = video_task_id

        # 保存任务到文件
        save_tasks()

        return jsonify({
            'extend_task_id': extend_task_id,
            'video_task_id': video_task_id,
            'uploaded_video_id': upload_video_id,
            'uploaded_video_url': f'/outputs/{video_filename}',
            'message': '上传视频延长任务已启动',
            'steps': tasks[extend_task_id]['steps']
        })

    except Exception as e:
        print(f"❌ 上传视频延长API异常: {e}")
        return jsonify({'error': f'上传视频延长失败: {str(e)}'}), 500

if __name__ == '__main__':
    print("启动图片转视频应用...")
    print("请确保已设置正确的API Key")

    # 加载持久化的任务
    load_tasks()

    print("访问 http://localhost:5000 开始使用")
    app.run(debug=True, host='0.0.0.0', port=5000)