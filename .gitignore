# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Application specific
uploads/*.png
uploads/*.jpg
uploads/*.jpeg
uploads/*.gif
uploads/*.bmp
uploads/*.webp
outputs/*.mp4
outputs/*.jpg
outputs/*.png
temp_video_processing/
downloads/
tasks_data.pkl

# Logs
*.log
app.log

# Config (if contains sensitive data)
# config.py

# Temporary files
*.tmp
*.temp
