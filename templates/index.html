<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>图片转视频生成器</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif; background: #f7fafd; margin: 0; padding: 0; }
        .main-layout { display: flex; max-width: 900px; margin: 24px auto; gap: 18px; }
        .sidebar { width: 220px; min-width: 180px; background: #fafdff; border-radius: 10px; box-shadow: 0 2px 8px #0078ff11; padding: 14px 10px 10px 10px; display: flex; flex-direction: column; }
        .main-content { flex: 1; min-width: 0; }
        .container { max-width: 100%; margin: 0; background: none; border-radius: 0; box-shadow: none; padding: 0; }
        h2 { margin-top: 0; letter-spacing: 1px; font-weight: 700; color: #1a2a3a; font-size: 1.4rem; margin-bottom: 16px; }
        .section { margin-bottom: 18px; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #0078ff11; padding: 18px 14px 12px 14px; border-left: 3px solid #0078ff; position: relative; }
        .section:not(:first-child) { margin-top: 16px; }
        .section > div:first-child { font-size: 15px; font-weight: 600; color: #0078ff; margin-bottom: 10px; }
        .product-list { display: flex; gap: 8px; flex-wrap: wrap; }
        .product-card { border: 1.5px solid #eee; border-radius: 8px; padding: 7px 12px; cursor: pointer; transition: border 0.2s, box-shadow 0.2s; background: #f5faff; box-shadow: 0 1px 2px #0078ff11; display: flex; align-items: center; font-size: 0.98rem; }
        .product-card.selected { border: 2px solid #0078ff; background: #eaf4ff; box-shadow: 0 2px 6px #0078ff22; }
        .product-card img { border-radius: 6px; margin-right: 8px; width: 32px; }
        .directions { display: flex; flex-wrap: wrap; gap: 8px; }
        .direction-card { border: 1px solid #ddd; border-radius: 8px; padding: 8px 12px; cursor: pointer; background: #f9fafd; width: 48%; transition: border 0.2s, box-shadow 0.2s; box-shadow: 0 1px 2px #0078ff11; font-size: 0.98rem; }
        .direction-card.selected { border: 2px solid #0078ff; background: #eaf4ff; box-shadow: 0 2px 6px #0078ff22; }
        .img-list { 
            display: grid; 
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); 
            gap: 16px; 
            margin-bottom: 16px; 
        }
        .img-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 8px #0078ff11;
            padding: 12px;
            transition: box-shadow 0.2s, transform 0.15s;
            cursor: pointer;
            border: 2px solid transparent;
        }
        .img-item:hover {
            box-shadow: 0 4px 16px #0078ff22;
            transform: translateY(-2px);
        }
        .img-item.selected {
            border: 2px solid #0078ff;
            background: #f0f8ff;
        }
        .img-item img {
            max-width: 648px;
            max-height: 1152px;
            width: auto;
            height: auto;
            display: block;
            margin: 0 auto 8px auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px #0078ff22;
            background: #fff;
        }
        .img-actions {
            display: flex;
            gap: 8px;
            justify-content: center;
            width: 100%;
            margin-top: 8px;
        }
        .img-actions .btn {
            padding: 4px 8px;
            font-size: 12px;
            min-width: auto;
        }
        .img-actions .copy-btn {
            padding: 4px 8px;
            font-size: 12px;
        }
        .controls { display: flex; gap: 8px; align-items: center; margin-bottom: 10px; flex-wrap: wrap; }
        .btn { background: linear-gradient(90deg,#0078ff 60%,#00c6ff 100%); color: #fff; border: none; border-radius: 7px; padding: 7px 16px; font-size: 15px; font-weight: 600; cursor: pointer; transition: background 0.2s, box-shadow 0.2s, transform 0.15s; box-shadow: 0 1px 2px #0078ff22; }
        .btn:disabled { background: #b3c6d9; cursor: not-allowed; }
        .btn:hover:not(:disabled) { background: linear-gradient(90deg,#005bb5 60%,#0099cc 100%); transform: scale(1.04); }
        .upload-label { display: inline-block; background: #eaf4ff; border-radius: 7px; padding: 6px 12px; cursor: pointer; margin-left: 8px; font-weight: 500; color: #0078ff; border: 1px solid #b3d8ff; transition: background 0.2s, border 0.2s; font-size: 0.98rem; }
        .upload-label:hover { background: #d2eaff; border: 1px solid #0078ff; }
        input[type='file'] { display: none; }
        .tip { color: #888; font-size: 13px; margin-top: 2px; }
        input, textarea, select { border: 1px solid #b3d8ff; border-radius: 7px; padding: 6px 10px; font-size: 15px; margin-right: 6px; margin-bottom: 6px; outline: none; transition: border 0.2s, box-shadow 0.2s; background: #fafdff; box-shadow: 0 1px 2px #0078ff11; }
        input:focus, textarea:focus, select:focus { border: 1px solid #0078ff; box-shadow: 0 2px 6px #0078ff22; }
        select[multiple] { height: 110px; min-width: 120px; }
        .video-preview { margin-top: 14px; }
        .status-label { display: inline-block; padding: 2px 8px; border-radius: 7px; font-size: 13px; font-weight: 600; margin-right: 6px; }
        .status-label.processing { background: #fffbe6; color: #bfa100; border: 1px solid #ffe58f; }
        .status-label.completed { background: #e6fffb; color: #08979c; border: 1px solid #87e8de; }
        .status-label.failed { background: #fff1f0; color: #cf1322; border: 1px solid #ffa39e; }
        .video-task-card { background: #fafdff; border: 1px solid #b3d8ff; border-radius: 7px; padding: 6px 10px; margin: 6px 0; font-size: 13px; display: flex; align-items: center; justify-content: space-between; box-shadow: 0 1px 2px #0078ff11; }
        .video-task-id { font-family: monospace; color: #0078ff; font-size: 13px; user-select: all; cursor: pointer; }
        .copy-btn { background: #eaf4ff; color: #0078ff; border: none; border-radius: 6px; padding: 3px 10px; font-size: 13px; margin-left: 6px; cursor: pointer; transition: background 0.2s, transform 0.15s; }
        .copy-btn:hover { background: #d2eaff; transform: scale(1.06); }
        /* 历史区优化 */
        #historyList {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(210px, 1fr));
          gap: 16px;
          margin-top: 8px;
        }
        .history-card {
          background: #fff;
          border-radius: 16px;
          box-shadow: 0 2px 12px #0078ff11;
          overflow: hidden;
          transition: box-shadow 0.2s, transform 0.15s, border 0.2s;
          border: 1.5px solid #f0f0f0;
          display: flex;
          flex-direction: column;
          /* min-height: 260px; */
          position: relative;
          align-items: center;
          padding-bottom: 0;
        }
        .history-card:hover {
          box-shadow: 0 8px 32px #0078ff33;
          border: 1.5px solid #0078ff;
          transform: translateY(-2px) scale(1.03);
        }
        .history-thumb {
          width: 120px;
          height: 213px;
          object-fit: cover;
          border-radius: 12px 12px 0 0;
          background: #fafdff;
          display: block;
          margin: 0 auto;
        }
        .history-info {
          padding: 10px 14px 8px 14px;
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
        }
        .history-desc {
          display: none;
        }
        .history-meta {
          font-size: 13px;
          color: #888;
          margin-bottom: 0;
          margin-top: 2px;
          text-align: right;
        }
        .history-actions {
          display: flex;
          gap: 6px;
          margin-top: 2px;
          opacity: 0;
          pointer-events: none;
          transition: opacity 0.2s;
          justify-content: flex-end;
        }
        .history-card:hover .history-actions {
          opacity: 1;
          pointer-events: auto;
        }
        .history-actions .btn {
          font-size: 12px;
          padding: 4px 12px;
          border-radius: 7px;
          background: linear-gradient(90deg,#0078ff 60%,#00c6ff 100%);
          color: #fff;
          border: none;
          cursor: pointer;
          transition: background 0.2s, transform 0.15s;
          box-shadow: 0 1px 2px #0078ff22;
        }
        .history-actions .btn:hover {
          background: linear-gradient(90deg,#005bb5 60%,#0099cc 100%);
          transform: scale(1.06);
        }
        @media (max-width: 900px) {
          #historyList {
            grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
          }
          .history-thumb { height: 90px; }
        }
        @media (max-width: 600px) {
          #historyList {
            grid-template-columns: 1fr;
          }
          .history-card { min-height: 180px; }
          .history-thumb { height: 60px; }
        }
        .history-clear-btn { float: right; background: #fff0f0; color: #cf1322; border: 1px solid #ffa39e; border-radius: 6px; padding: 3px 10px; font-size: 12px; margin-bottom: 6px; cursor: pointer; transition: background 0.2s, border 0.2s; }
        .history-clear-btn:hover { background: #ffeaea; border: 1px solid #cf1322; }
        .history-toggle-btn { background: #fafdff; color: #0078ff; border: 1px solid #b3d8ff; border-radius: 6px; padding: 2px 8px; font-size: 12px; margin-bottom: 6px; cursor: pointer; transition: background 0.2s, border 0.2s; float: left; }
        .history-toggle-btn:hover { background: #eaf4ff; border: 1px solid #0078ff; }
        @media (max-width: 1200px) {
            .main-layout { flex-direction: column; gap: 0; }
            .sidebar { width: 100%; min-width: 0; margin-bottom: 12px; }
            .main-content { width: 100%; }
            #historyList { flex-direction: column; gap: 6px; }
            .history-card { width: 100%; min-width: 0; }
        }
        /* 预览大图弹窗 */
        #historyImgPreviewModal {
          position: fixed; left: 0; top: 0; width: 100vw; height: 100vh;
          background: rgba(0,0,0,0.7); z-index: 9999; display: flex; align-items: center; justify-content: center;
          display: none;
        }
        #historyImgPreviewModal img {
          max-width: 90vw; max-height: 90vh; border-radius: 12px; box-shadow: 0 4px 32px #0008;
        }
        #closeHistoryImgPreview {
          position: absolute; top: 30px; right: 40px; background: #fff; color: #0078ff; border: none; border-radius: 50%; width: 36px; height: 36px; font-size: 22px; cursor: pointer; box-shadow: 0 2px 8px #0003;
        }

        /* 视频拼接样式 */
        .video-item {
          background: #fff;
          border: 2px solid #e6f3ff;
          border-radius: 12px;
          padding: 12px;
          cursor: pointer;
          transition: all 0.2s;
          text-align: center;
        }
        .video-item:hover {
          border-color: #0078ff;
          box-shadow: 0 4px 12px #0078ff22;
        }
        .video-item.selected {
          border-color: #0078ff;
          background: #f0f8ff;
        }
        .video-item img {
          width: 100%;
          height: 100px;
          object-fit: cover;
          border-radius: 8px;
          margin-bottom: 8px;
        }
        .video-item .video-info {
          font-size: 12px;
          color: #666;
        }
        .selected-video-tag {
          background: #0078ff;
          color: #fff;
          padding: 4px 8px;
          border-radius: 16px;
          font-size: 12px;
          display: inline-flex;
          align-items: center;
          gap: 4px;
        }
        .selected-video-tag .remove-btn {
          background: rgba(255,255,255,0.3);
          border: none;
          border-radius: 50%;
          width: 16px;
          height: 16px;
          font-size: 10px;
          cursor: pointer;
          color: #fff;
        }

        /* 尾帧提取样式 */
        .frame-video-item {
          background: #fff;
          border: 2px solid #e6ffe6;
          border-radius: 12px;
          padding: 12px;
          text-align: center;
          transition: all 0.2s;
          position: relative;
        }
        .frame-video-item:hover {
          border-color: #52c41a;
          box-shadow: 0 4px 12px #52c41a22;
        }
        .frame-video-item img {
          width: 100%;
          height: 100px;
          object-fit: cover;
          border-radius: 8px;
          margin-bottom: 8px;
        }
        .frame-video-item .video-info {
          font-size: 12px;
          color: #666;
          margin-bottom: 8px;
        }
        .frame-video-item .extract-btn {
          background: #52c41a;
          color: #fff;
          border: none;
          padding: 6px 12px;
          border-radius: 6px;
          font-size: 12px;
          cursor: pointer;
          transition: all 0.2s;
        }
        .frame-video-item .extract-btn:hover {
          background: #389e0d;
        }
        .frame-video-item .extract-btn:disabled {
          background: #d9d9d9;
          cursor: not-allowed;
        }
        .frame-result {
          background: #f6ffed;
          border: 1px solid #b7eb8f;
          border-radius: 8px;
          padding: 12px;
          margin-top: 8px;
        }

        /* 延长生成按钮样式 */
        .extend-btn {
          background: #52c41a !important;
          color: #fff !important;
          border: none;
          padding: 6px 12px;
          border-radius: 6px;
          font-size: 12px;
          cursor: pointer;
          transition: all 0.2s;
        }
        .extend-btn:hover {
          background: #389e0d !important;
          transform: scale(1.05);
        }
        .extend-btn:disabled {
          background: #d9d9d9 !important;
          cursor: not-allowed;
          transform: none;
        }

        /* 上传视频延长区域样式 */
        #uploadVideoExtendSection {
          background: #fff7e6;
          border-left: 4px solid #fa8c16;
        }
        #uploadVideoExtendSection .btn {
          background: linear-gradient(90deg, #fa8c16 60%, #ffa940 100%);
        }
        #uploadVideoExtendSection .btn:hover:not(:disabled) {
          background: linear-gradient(90deg, #d46b08 60%, #fa8c16 100%);
        }
    </style>
    <!-- 优化图片和卡片样式 -->
    <style>
        .img-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            max-width: 260px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 8px #0078ff11;
            padding: 12px 0 10px 0;
            margin: 0 auto;
        }
        .img-item img {
            display: block;
            max-width: 220px;
            width: 100%;
            height: auto;
            margin: 0 auto 12px auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px #0078ff22;
            background: #fff;
        }
    </style>
    /* 图片操作区UI优化 */
    <style>
      .img-actions {
        display: flex;
        gap: 12px;
        justify-content: center;
        margin-top: 10px;
        margin-bottom: 2px;
      }
      .img-actions .btn {
        background: linear-gradient(90deg,#0078ff 60%,#00c6ff 100%);
        color: #fff;
        border: none;
        border-radius: 8px;
        padding: 7px 18px;
        font-size: 15px;
        font-weight: 600;
        cursor: pointer;
        transition: background 0.2s, box-shadow 0.2s, transform 0.15s;
        box-shadow: 0 2px 8px #0078ff22;
        margin: 0 2px;
      }
      .img-actions .btn:disabled { background: #b3c6d9; cursor: not-allowed; }
      .img-actions .btn:hover:not(:disabled) { background: linear-gradient(90deg,#005bb5 60%,#0099cc 100%); transform: scale(1.04); }
      #imageActionsContainer {
        background: #fafdff;
        border-radius: 14px;
        box-shadow: 0 2px 8px #0078ff11;
        margin-top: 18px;
        padding: 18px 12px 10px 12px;
      }
    </style>

    /* 优化图片卡片 */
    <style>
      .img-item {
        background: #fff;
        border-radius: 14px;
        box-shadow: 0 2px 8px #0078ff11;
        padding: 12px 0 10px 0;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        max-width: 260px;
      }
      .img-item img {
        max-width: 220px;
        width: 100%;
        height: auto;
        margin: 0 auto 12px auto;
        border-radius: 8px;
        box-shadow: 0 2px 8px #0078ff22;
        background: #fff;
      }
    </style>
    <style>
        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 18px 12px;
            align-items: flex-end;
            margin-bottom: 10px;
        }
        .var-group {
            display: flex;
            align-items: flex-end;
            gap: 6px;
            margin-bottom: 2px;
        }
        .var-group label {
            min-width: 38px;
            font-weight: 500;
            margin-bottom: 0;
        }
        .var-group select, .var-group input[type='text'] {
            margin-bottom: 0;
        }
        @media (max-width: 700px) {
            .controls { flex-direction: column; gap: 8px 0; align-items: stretch; }
            .var-group { flex-direction: column; align-items: flex-start; gap: 2px; }
        }
        .img-list {
            display: flex;
            flex-wrap: wrap;
            gap: 18px;
            justify-content: flex-start;
            margin-bottom: 16px;
        }
        .img-item {
            background: #fff;
            border-radius: 14px;
            box-shadow: 0 2px 8px #0078ff11;
            padding: 12px 0 10px 0;
            margin: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            max-width: 220px;
            min-width: 180px;
            width: 100%;
        }
        .img-item img {
            max-width: 200px;
            width: 100%;
            height: auto;
            margin: 0 auto 12px auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px #0078ff22;
            background: #fff;
        }
        @media (max-width: 700px) {
            .img-list { flex-direction: column; gap: 10px; }
            .img-item { max-width: 100%; min-width: 0; }
        }
    </style>
</head>
<body>
<div class="main-layout">
    <!-- 移除sidebar历史区 -->
    <div class="main-content">
<div class="container">
    <h2>图片转视频生成器</h2>

    <!-- 产品选择 -->
    <div class="section">
        <div><b>1. 选择产品</b></div>
        <div id="productList" class="product-list"></div>
    </div>

    <!-- 产品信息填写 -->
    <div class="section">
        <div><b>2. 填写产品信息</b> <span class="tip">(可自动填充)</span></div>
        <input id="productName" placeholder="产品名" style="width: 180px;">
        <input id="ingredients" placeholder="成分" style="width: 180px;">
        <input id="benefits" placeholder="核心卖点" style="width: 180px;">
        <input id="usageScene" placeholder="使用场景" style="width: 180px;">
        <button class="btn" id="getDirectionsBtn">获取创意方向</button>
    </div>

    <!-- 创意方向选择 -->
    <div class="section" id="directionsSection">
        <div><b>3. 选择创意方向</b></div>
        <div id="directions" class="directions"></div>
    </div>

    <!-- 图片生成与人种/直出模式切换 -->
    <div class="section" id="imageGenSection">
        <div class="controls">
            <div class="var-group">
                <label for="ageVarSelect">年龄:</label>
                <select id="ageVarSelect" multiple size="3" style="height:60px;width:110px;"></select>
                <input type="text" id="ageVarManual" placeholder="手动输入年龄" style="width:80px;">
                <button type="button" class="btn" id="saveAgeVarBtn" style="padding:4px 10px;font-size:12px;">保存</button>
            </div>
            <div class="var-group">
                <label for="raceVarSelect">人种:</label>
                <select id="raceVarSelect" multiple size="6" style="height:120px;width:180px;"></select>
                <input type="text" id="raceVarManual" placeholder="手动输入人种" style="width:80px;">
                <button type="button" class="btn" id="saveRaceVarBtn" style="padding:4px 10px;font-size:12px;">保存</button>
            </div>
            <div class="var-group">
                <label for="genderVarSelect">性别:</label>
                <select id="genderVarSelect" multiple size="2" style="height:60px;width:90px;"></select>
                <input type="text" id="genderVarManual" placeholder="手动输入性别" style="width:60px;">
                <button type="button" class="btn" id="saveGenderVarBtn" style="padding:4px 10px;font-size:12px;">保存</button>
            </div>
            <div class="var-group">
                <label for="occupationVarSelect">职业:</label>
                <select id="occupationVarSelect" multiple size="3" style="height:60px;width:130px;"></select>
                <input type="text" id="occupationVarManual" placeholder="手动输入职业" style="width:90px;">
                <button type="button" class="btn" id="saveOccupationVarBtn" style="padding:4px 10px;font-size:12px;">保存</button>
            </div>
            <div class="var-group">
                <label for="bgVarSelect">背景:</label>
                <select id="bgVarSelect" multiple size="3" style="height:60px;width:120px;"></select>
                <input type="text" id="bgVarManual" placeholder="手动输入背景" style="width:90px;">
                <button type="button" class="btn" id="saveBgVarBtn" style="padding:4px 10px;font-size:12px;">保存</button>
            </div>
            <div class="var-group" style="align-items:center;">
                <input type="checkbox" id="directMode"> <span style="margin-left:2px;">直出模式</span>
            </div>
            <button class="btn" id="genImagesBtn">生成图片</button>
        </div>
        <div id="imgList" class="img-list"></div>
        <div>
            <label class="upload-label">
                上传图片
                <input type="file" id="uploadInput" accept="image/*">
            </label>
        </div>
    </div>

    <!-- 视频生成 -->
    <div class="section" id="videoGenSection">
        <div style="margin-bottom:16px;">
            <div style="font-weight:600;color:#0078ff;margin-bottom:8px;">视频生成方式</div>
            <div style="display:flex;gap:12px;align-items:center;flex-wrap:wrap;">
                <label for="videoDurationSelectImg" style="font-weight:500;">时长:</label>
                <select id="videoDurationSelectImg" style="width:80px;">
                    <option value="6">6秒</option>
                    <option value="10">10秒</option>
                </select>
                <button class="btn" id="genVideoBtn" disabled>从图片生成视频</button>
                <button class="btn" id="genVideoFromTextBtn">从文本生成视频</button>
            </div>
        </div>
        
        <!-- 纯文本视频生成区域 -->
        <div id="textVideoGenArea" style="display:none;margin-bottom:16px;padding:12px;background:#f7f7f7;border-radius:8px;">
            <div style="font-weight:600;color:#0078ff;margin-bottom:8px;">文本视频生成</div>
            <textarea id="videoPromptInput" placeholder="请输入视频生成提示词..." style="width:100%;min-height:80px;margin-bottom:8px;"></textarea>
            <div style="display:flex;gap:8px;align-items:center;flex-wrap:wrap;">
                <label>模型: <select id="videoModelSelect" style="width:120px;">
                    <option value="MiniMax-Hailuo-02">MiniMax-Hailuo-02</option>
                </select></label>
                <label>分辨率: <select id="videoResolutionSelect" style="width:100px;">
                    <option value="768P">768P</option>
                    <option value="1080P">1080P</option>
                </select></label>
                <button class="btn" id="submitTextVideoBtn">生成视频</button>
            </div>
        </div>
        
        <div id="videoStatus"></div>
        <div class="video-preview" id="videoPreview"></div>
        <!-- 新增：视频预览功能区 -->
        <div style="margin-top:24px;padding:16px 0 0 0;border-top:1.5px dashed #b3d8ff;">
            <div style="font-weight:600;color:#0078ff;margin-bottom:8px;">视频预览</div>
            <div style="display:flex;gap:12px;align-items:center;flex-wrap:wrap;">
                <input id="previewTaskIdInput" placeholder="输入任务ID预览" style="width:180px;">
                <button class="btn" id="previewTaskIdBtn" style="padding:6px 16px;font-size:15px;">预览任务ID视频</button>
                <button class="btn" id="testProgressBtn" style="padding:6px 16px;font-size:15px;background:#ff6b35;">测试进度查询</button>
                <label class="upload-label" style="margin-left:0;">
                    选择本地视频
                    <input type="file" id="localVideoInput" accept="video/mp4">
                </label>
            </div>
            <div id="customVideoPreview" style="margin-top:16px;"></div>
        </div>
    </div>

    <!-- 上传视频延长功能区 -->
    <div class="section" id="uploadVideoExtendSection" style="margin-top:32px;background:#fff7e6;border-left:4px solid #fa8c16;">
        <div style="font-weight:600;color:#fa8c16;margin-bottom:16px;">📤 上传视频延长</div>

        <div style="margin-bottom:16px;color:#666;font-size:14px;">
            上传一个视频文件，系统将自动提取尾帧、生成续集视频并进行无缝拼接
        </div>

        <div style="margin-bottom:16px;">
            <input type="file" id="uploadVideoExtendInput" accept="video/mp4" style="margin-bottom:8px;">
            <div style="display:flex;gap:8px;align-items:center;flex-wrap:wrap;margin-bottom:8px;">
                <input id="uploadExtendPrompt" placeholder="延长生成提示词" style="width:200px;" value="继续生成视频内容">
                <select id="uploadExtendDuration" style="width:80px;">
                    <option value="6">6秒</option>
                    <option value="10">10秒</option>
                </select>
                <button id="uploadVideoExtendBtn" class="btn" onclick="uploadVideoExtend()" disabled>开始延长</button>
            </div>
        </div>

        <div id="uploadExtendPreview" style="margin-bottom:16px;"></div>
        <div id="uploadExtendResult" style="margin-top:16px;"></div>
    </div>

    <!-- 尾帧提取功能区 -->
    <div class="section" id="lastFrameSection" style="margin-top:32px;background:#f0fff0;border-left:4px solid #52c41a;">
        <div style="font-weight:600;color:#52c41a;margin-bottom:16px;">🖼️ 视频尾帧提取</div>

        <div style="margin-bottom:16px;">
            <button id="loadVideosForFrameBtn" class="btn" onclick="loadVideosForFrameExtraction()">加载可用视频</button>
            <span id="frameExtractionStatus" style="margin-left:12px;color:#666;"></span>
        </div>

        <div id="frameExtractionArea" style="display:none;">
            <div style="margin-bottom:12px;color:#666;font-size:14px;">
                选择要提取尾帧的视频：
            </div>
            <div id="videosForFrameList" style="display:grid;grid-template-columns:repeat(auto-fill,minmax(200px,1fr));gap:12px;margin-bottom:16px;"></div>
        </div>

        <div id="frameExtractionResult" style="margin-top:16px;"></div>
    </div>

    <!-- 视频拼接功能区 -->
    <div class="section" id="videoConcatSection" style="margin-top:32px;background:#f0f8ff;border-left:4px solid #0078ff;">
        <div style="font-weight:600;color:#0078ff;margin-bottom:16px;">🎬 视频无缝拼接</div>

        <div style="margin-bottom:16px;">
            <button id="loadVideosBtn" class="btn" onclick="loadAvailableVideos()">加载可用视频</button>
            <button id="seamlessConcatBtn" class="btn" onclick="performSeamlessConcat()" disabled>开始无缝拼接</button>
            <span id="concatStatus" style="margin-left:12px;color:#666;"></span>
        </div>

        <div id="videoSelectionArea" style="display:none;">
            <div style="margin-bottom:12px;color:#666;font-size:14px;">
                选择要拼接的视频（至少选择2个，按选择顺序拼接）：
            </div>
            <div id="availableVideosList" style="display:grid;grid-template-columns:repeat(auto-fill,minmax(200px,1fr));gap:12px;margin-bottom:16px;"></div>

            <div style="margin-bottom:12px;">
                <strong>已选择的视频：</strong>
                <span id="selectedVideoCount">0</span> 个
            </div>
            <div id="selectedVideosList" style="display:flex;flex-wrap:wrap;gap:8px;margin-bottom:16px;"></div>
        </div>

        <div id="concatResult" style="margin-top:16px;"></div>
    </div>

    <!-- 历史生成记录区（只保留主内容区底部） -->
    <div class="section" id="historySection" style="margin-bottom:0;margin-top:32px;background:#fafdff;border-left:4px solid #aaa;">
        <div style="font-weight:600;color:#444;margin-bottom:8px;">历史生成记录 <button class="history-clear-btn" onclick="clearHistory()">清空历史</button></div>
        <div id="historyList"></div>
    </div>
</div>

<script>
let products = [];
let directions = [];
let selectedProduct = null;
let selectedDirection = null;
let selectedRace = 'Black';
let directMode = false;
let generatedImages = [];
let selectedImageUrl = null;
let uploadedImageFile = null;
let lastPrompt = '';

// 新增：创意转生图提示词并中英显示
let currentPromptZh = '';
let currentPromptEn = '';

// 在 directionsSection 下方加一个预览区和可编辑输入框
if(!$('promptPreview')) {
    const previewDiv = document.createElement('div');
    previewDiv.id = 'promptPreview';
    previewDiv.style = 'margin:12px 0 0 0;padding:10px;background:#f7f7f7;border-radius:6px;font-size:15px;color:#333;';
    $('directionsSection').appendChild(previewDiv);
    // 新增：可编辑英文prompt输入框
    const editDiv = document.createElement('div');
    editDiv.style = 'margin:8px 0 0 0;';
    editDiv.innerHTML = `<b>可编辑生图英文提示词：</b><br><textarea id='promptEdit' style='width:98%;min-height:48px;font-size:15px;border-radius:6px;padding:6px;margin-top:2px;'></textarea>`;
    $('directionsSection').appendChild(editDiv);
}

// 新增：视频数量输入
if(!$('videoCountInput')) {
    const input = document.createElement('input');
    input.type = 'number';
    input.id = 'videoCountInput';
    input.value = 1;
    input.min = 1;
    input.max = 10;
    input.style = 'width:60px;margin-left:10px;';
    const label = document.createElement('label');
    label.innerText = '生成视频数量:';
    label.htmlFor = 'videoCountInput';
    label.style = 'margin-left:10px;';
    const container = document.getElementById('videoGenSection') || document.body;
    container.insertBefore(label, container.firstChild);
    container.insertBefore(input, label.nextSibling);
}

function $(id) { return document.getElementById(id); }

// 1. 加载产品
// 产品选择后自动设置性别选择逻辑
function updateGenderSelectByProduct() {
    const genderSel = document.getElementById('genderVarSelect');
    if (!genderSel) return;
    if (selectedProduct && selectedProduct.name === 'Vital Oasis') {
        // 蓝色产品，只能选男性且不可更改
        Array.from(genderSel.options).forEach(opt => {
            if (opt.value === 'male') {
                opt.selected = true;
                opt.disabled = false;
            } else {
                opt.selected = false;
                opt.disabled = true;
            }
        });
        genderSel.style.background = '#eaf4ff';
    } else {
        // 其他产品，默认女性，可多选
        Array.from(genderSel.options).forEach(opt => {
            opt.disabled = false;
            opt.selected = (opt.value === 'female');
        });
        genderSel.style.background = '';
    }
}
// 产品卡片点击后调用
fetch('/products').then(r=>r.json()).then(list=>{
    products = list;
    $('productList').innerHTML = '';
    list.forEach((p,i)=>{
        const div = document.createElement('div');
        div.className = 'product-card';
        div.innerHTML = `<img src='${p.image_url}' style='width:40px;vertical-align:middle;margin-right:8px;'>${p.name}`;
        div.onclick = ()=>{
            document.querySelectorAll('.product-card').forEach(e=>e.classList.remove('selected'));
            div.classList.add('selected');
            selectedProduct = p;
            $('productName').value = p.name;
            updateGenderSelectByProduct(); // 新增：切换产品时刷新性别
        };
        $('productList').appendChild(div);
    });
    // 页面初始时也刷新一次
    updateGenderSelectByProduct();
});

// 2. 获取创意方向
$('getDirectionsBtn').onclick = ()=>{
    const product_name = $('productName').value.trim();
    const ingredients = $('ingredients').value.trim();
    const benefits = $('benefits').value.trim();
    const usage_scene = $('usageScene').value.trim();
    if(!product_name) return alert('请填写产品名');
    
    // 禁用按钮并显示进度条
    $('getDirectionsBtn').disabled = true;
    $('getDirectionsBtn').innerText = '生成中...';
    
    // 显示进度条
    const progressHtml = `
        <div style="margin-top:12px;padding:12px;background:#f7f7f7;border-radius:8px;">
            <div style="font-weight:600;color:#0078ff;margin-bottom:8px;">创意生成进度</div>
            <div style="height:16px;background:#eaf4ff;border-radius:8px;overflow:hidden;margin-bottom:8px;">
                <div id="creativeProgressBar" style="height:100%;width:0%;background:#0078ff;transition:width 0.3s;"></div>
            </div>
            <div id="creativeProgressText" style="text-align:center;font-size:13px;color:#0078ff;">正在生成创意方向...</div>
        </div>
    `;
    
    // 清除之前的进度条
    const existingProgress = document.getElementById('creativeProgressContainer');
    if (existingProgress) existingProgress.remove();
    
    // 添加新的进度条
    const progressContainer = document.createElement('div');
    progressContainer.id = 'creativeProgressContainer';
    progressContainer.innerHTML = progressHtml;
    $('directionsSection').appendChild(progressContainer);
    
    // 模拟进度条
    let progress = 0;
    const progressTimer = setInterval(() => {
        if (progress < 90) {
            progress += 2;
            document.getElementById('creativeProgressBar').style.width = progress + '%';
            document.getElementById('creativeProgressText').innerText = `正在生成创意方向... ${progress}%`;
        }
    }, 100);
    
    fetch('/creative_directions', {
        method:'POST',
        headers:{'Content-Type':'application/json'},
        body: JSON.stringify({product_name, ingredients, benefits, usage_scene})
    }).then(r=>r.json()).then(res=>{
        clearInterval(progressTimer);
        
        // 完成进度条
        document.getElementById('creativeProgressBar').style.width = '100%';
        document.getElementById('creativeProgressText').innerText = '创意生成完成！';
        
        setTimeout(() => {
            // 移除进度条
            const progressContainer = document.getElementById('creativeProgressContainer');
            if (progressContainer) progressContainer.remove();
        }, 1000);
        
        $('getDirectionsBtn').disabled = false;
        $('getDirectionsBtn').innerText = '获取创意方向';
        
        if(res.result && Array.isArray(res.result)) {
            directions = res.result;
            $('directions').innerHTML = '';
            directions.forEach((d,i)=>{
                const card = document.createElement('div');
                card.className = 'direction-card';
                card.innerHTML = `<b>${d.title}</b><br><span style='font-size:13px;'>${d.hook_line}</span>`;
                card.onclick = ()=>{
                    document.querySelectorAll('.direction-card').forEach(e=>e.classList.remove('selected'));
                    card.classList.add('selected');
                    selectedDirection = d;
                    
                    // 设置默认变量选择
                    setDefaultVariables();
                    
                    fetchPromptForCreative(d);
                };
                $('directions').appendChild(card);
            });
        } else {
            alert('获取创意方向失败');
        }
    }).catch(()=>{
        clearInterval(progressTimer);
        
        // 移除进度条
        const progressContainer = document.getElementById('creativeProgressContainer');
        if (progressContainer) progressContainer.remove();
        
        $('getDirectionsBtn').disabled = false;
        $('getDirectionsBtn').innerText = '获取创意方向';
        alert('获取创意方向失败');
    });
};

// 新增：设置默认变量选择
function setDefaultVariables() {
    // 设置默认人种变量：北美黑人
    const raceSelect = document.getElementById('raceVarSelect');
    if (raceSelect) {
        // 清除所有选择
        Array.from(raceSelect.options).forEach(opt => opt.selected = false);
        // 选择北美黑人
        const africanAmericanOption = Array.from(raceSelect.options).find(opt => 
            opt.value === 'African-American' || opt.text.includes('北美黑人')
        );
        if (africanAmericanOption) {
            africanAmericanOption.selected = true;
        }
    }
    
    // 设置默认背景变量：客厅
    const bgSelect = document.getElementById('bgVarSelect');
    if (bgSelect) {
        // 清除所有选择
        Array.from(bgSelect.options).forEach(opt => opt.selected = false);
        // 选择客厅
        const livingRoomOption = Array.from(bgSelect.options).find(opt => 
            opt.value === 'living room' || opt.text.includes('客厅')
        );
        if (livingRoomOption) {
            livingRoomOption.selected = true;
        }
    }
    
    // 设置默认性别变量：女性
    const genderSelect = document.getElementById('genderVarSelect');
    if (genderSelect) {
        // 清除所有选择
        Array.from(genderSelect.options).forEach(opt => opt.selected = false);
        // 选择女性
        const femaleOption = Array.from(genderSelect.options).find(opt => 
            opt.value === 'female' || opt.text.includes('女性')
        );
        if (femaleOption) {
            femaleOption.selected = true;
        }
    }
    
    
    // 年龄变量选择 + 手动输入
    if(!document.getElementById('ageVarSelect')) {
        const ageSel = document.createElement('select');
        ageSel.id = 'ageVarSelect';
        ageSel.multiple = true;
        ageSel.size = 3;
        ageSel.style = 'height:60px;width:110px;margin-right:10px;';
        [
            {value:'young', label:'年轻 (Young)'},
            {value:'middle-aged', label:'中年 (Middle-aged)'},
            {value:'elderly', label:'老年 (Elderly)'}
        ].forEach(a=>{
            const opt = document.createElement('option');
            opt.value = a.value;
            opt.innerText = a.label;
            ageSel.appendChild(opt);
        });
        const label = document.createElement('label');
        label.innerText = '年龄:';
        label.htmlFor = 'ageVarSelect';
        label.style = 'margin-right:6px;';
        const input = document.createElement('input');
        input.type = 'text';
        input.id = 'ageVarManual';
        input.placeholder = '手动输入年龄';
        input.style = 'width:80px;margin-right:10px;';
        const container = document.querySelector('.controls');
        container.insertBefore(label, container.children[0]);
        container.insertBefore(ageSel, label.nextSibling);
        container.insertBefore(input, ageSel.nextSibling);
    }
    // 人种变量选择 + 手动输入
    if(!document.getElementById('raceVarSelect')) {
        const raceSel = document.createElement('select');
        raceSel.id = 'raceVarSelect';
        ['Caucasian','Latina','Asian'].forEach(r=>{
            const opt = document.createElement('option');
            opt.value = r;
            opt.innerText = r;
            raceSel.appendChild(opt);
        });
        const label = document.createElement('label');
        label.innerText = '人种:';
        label.htmlFor = 'raceVarSelect';
        label.style = 'margin-right:6px;';
        const input = document.createElement('input');
        input.type = 'text';
        input.id = 'raceVarManual';
        input.placeholder = '手动输入人种';
        input.style = 'width:80px;margin-right:10px;';
        const container = document.querySelector('.controls');
        container.insertBefore(label, container.children[2]);
        container.insertBefore(raceSel, label.nextSibling);
        container.insertBefore(input, raceSel.nextSibling);
    }
    // 性别变量选择 + 手动输入
    if(!document.getElementById('genderVarSelect')) {
        const genderSel = document.createElement('select');
        genderSel.id = 'genderVarSelect';
        genderSel.multiple = true;
        genderSel.size = 2;
        genderSel.style = 'height:60px;width:90px;margin-right:10px;';
        [
            {value:'female', label:'女性 (Female)'},
            {value:'male', label:'男性 (Male)'},
        ].forEach(g=>{
            const opt = document.createElement('option');
            opt.value = g.value;
            opt.innerText = g.label;
            genderSel.appendChild(opt);
        });
        const label = document.createElement('label');
        label.innerText = '性别:';
        label.htmlFor = 'genderVarSelect';
        label.style = 'margin-right:6px;';
        const input = document.createElement('input');
        input.type = 'text';
        input.id = 'genderVarManual';
        input.placeholder = '手动输入性别';
        input.style = 'width:60px;margin-right:10px;';
        const container = document.querySelector('.controls');
        container.insertBefore(label, container.children[4]);
        container.insertBefore(genderSel, label.nextSibling);
        container.insertBefore(input, genderSel.nextSibling);
    }
    // 职业描述选择 + 手动输入
    if(!document.getElementById('occupationVarSelect')) {
        const occSel = document.createElement('select');
        occSel.id = 'occupationVarSelect';
        occSel.multiple = true;
        occSel.size = 3;
        occSel.style = 'height:60px;width:130px;margin-right:10px;';
        [
            {value:'office worker', label:'上班族 (Office Worker)'},
            {value:'athlete', label:'运动员 (Athlete)'},
            {value:'student', label:'学生 (Student)'},
            {value:'doctor', label:'医生 (Doctor)'},
            {value:'teacher', label:'教师 (Teacher)'},
            {value:'retiree', label:'退休人士 (Retiree)'}
        ].forEach(o=>{
            const opt = document.createElement('option');
            opt.value = o.value;
            opt.innerText = o.label;
            occSel.appendChild(opt);
        });
        const label = document.createElement('label');
        label.innerText = '职业:';
        label.htmlFor = 'occupationVarSelect';
        label.style = 'margin-right:6px;';
        const input = document.createElement('input');
        input.type = 'text';
        input.id = 'occupationVarManual';
        input.placeholder = '手动输入职业';
        input.style = 'width:90px;margin-right:10px;';
        const container = document.querySelector('.controls');
        container.insertBefore(label, container.children[6]);
        container.insertBefore(occSel, label.nextSibling);
        container.insertBefore(input, occSel.nextSibling);
    }
    // 背景变量选择 + 手动输入
    if(!document.getElementById('bgVarSelect')) {
        const bgSel = document.createElement('select');
        bgSel.id = 'bgVarSelect';
        bgSel.multiple = true;
        bgSel.size = 3;
        bgSel.style = 'height:60px;width:120px;margin-right:10px;';
        [
            {value:'kitchen', label:'厨房 (Kitchen)'},
            {value:'living room', label:'客厅 (Living Room)'},
            {value:'bathroom', label:'浴室 (Bathroom)'},
            {value:'bedroom', label:'卧室 (Bedroom)'},
            {value:'outdoor', label:'户外 (Outdoor)'},
            {value:'studio', label:'摄影棚 (Studio)'}
        ].forEach(bg=>{
            const opt = document.createElement('option');
            opt.value = bg.value;
            opt.innerText = bg.label;
            bgSel.appendChild(opt);
        });
        const label = document.createElement('label');
        label.innerText = '背景:';
        label.htmlFor = 'bgVarSelect';
        label.style = 'margin-right:6px;';
        const input = document.createElement('input');
        input.type = 'text';
        input.id = 'bgVarManual';
        input.placeholder = '手动输入背景';
        input.style = 'width:90px;margin-right:10px;';
        const container = document.querySelector('.controls');
        container.insertBefore(label, container.children[6]);
        container.insertBefore(bgSel, label.nextSibling);
        container.insertBefore(input, bgSel.nextSibling);
    }
    
    // 显示变量设置提示
    showVariableSetNotification();
}

// 新增：显示变量设置提示
function showVariableSetNotification() {
    // 创建提示元素
    const notification = document.createElement('div');
    notification.id = 'variableNotification';
    notification.style = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #e6fffb;
        color: #08979c;
        border: 1px solid #87e8de;
        border-radius: 8px;
        padding: 12px 16px;
        font-size: 14px;
        font-weight: 600;
        z-index: 1000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        animation: slideIn 0.3s ease-out;
    `;
    notification.innerHTML = `
        <div style="display:flex;align-items:center;gap:8px;">
            <span>✅</span>
            <span>已自动设置默认变量：北美黑人 + 客厅 + 女性</span>
        </div>
    `;
    
    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    `;
    document.head.appendChild(style);
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }
    }, 3000);
}

// 新增：创意转生图提示词并中英显示
function fetchPromptForCreative(direction) {
    const payload = {
        visual_description: direction.visual_description || '',
        character: direction.character || '',
        environment: direction.environment || ''
    };
    fetch('/creative_directions_prompt', {
        method:'POST',
        headers:{'Content-Type':'application/json'},
        body: JSON.stringify(payload)
    }).then(r=>r.json()).then(res=>{
        if(res.prompt_zh && res.prompt_en) {
            currentPromptZh = res.prompt_zh;
            currentPromptEn = res.prompt_en;
            $('promptPreview').innerHTML = `<b>生图提示词（中）:</b> ${currentPromptZh}<br><b>生图提示词（英）:</b> ${currentPromptEn}`;
            $('promptEdit').value = currentPromptEn;
        } else {
            $('promptPreview').innerHTML = '未能生成生图提示词';
            $('promptEdit').value = '';
        }
    }).catch(()=>{
        $('promptPreview').innerHTML = '生图提示词生成失败';
        $('promptEdit').value = '';
    });
}

// 3. 生成图片（新接口）
// 产品颜色映射
const productColorMap = {
    'Blend Oasis': 'orange',
    'Detox Oasis': 'green',
    'Vital Oasis': 'blue'
};

// 人种变量映射（英文）
const raceVarMap = {
    'Caucasian': 'Caucasian',
    'Mediterranean': 'Mediterranean',
    'Latina': 'Latina',
    'African-American': 'African-American',
    'Afro-Latina': 'Afro-Latina',
    'Mixed-race': 'Mixed-race (European+Indigenous)'
};
// 性别变量映射（英文）
const genderVarMap = {
    'female': 'female',
    'male': 'male',
    'non-binary': 'non-binary'
};
// 背景变量映射（英文）
const bgVarMap = {
    'kitchen': 'kitchen',
    'living room': 'living room',
    'bathroom': 'bathroom',
    'bedroom': 'bedroom',
    'outdoor': 'outdoor',
    'studio': 'studio'
};


// 3. 批量生图时变量替换用英文短语
function getSelectedRaces() {
    const manual = document.getElementById('raceVarManual')?.value.trim();
    if(manual) return [manual];
    const sel = document.getElementById('raceVarSelect');
    if(!sel) return ['Caucasian'];
    return Array.from(sel.selectedOptions).map(o=>o.value);
}


// 补充：获取选择的性别和背景
function getSelectedGenders() {
    const manual = document.getElementById('genderVarManual')?.value.trim();
    if(manual) return [manual];
    const sel = document.getElementById('genderVarSelect');
    if(!sel) return ['female'];
    return Array.from(sel.selectedOptions).map(o=>o.value);
}
function getSelectedBgs() {
    const manual = document.getElementById('bgVarManual')?.value.trim();
    if(manual) return [manual];
    const sel = document.getElementById('bgVarSelect');
    if(!sel) return ['kitchen'];
    return Array.from(sel.selectedOptions).map(o=>o.value);
}

// Helper functions for new variables
function getSelectedAges() {
    const manual = document.getElementById('ageVarManual')?.value.trim();
    if(manual) return [manual];
    const sel = document.getElementById('ageVarSelect');
    if(!sel) return ['young'];
    return Array.from(sel.selectedOptions).map(o=>o.value);
}
function getSelectedOccupations() {
    const manual = document.getElementById('occupationVarManual')?.value.trim();
    if(manual) return [manual];
    const sel = document.getElementById('occupationVarSelect');
    if(!sel) return ['office worker'];
    return Array.from(sel.selectedOptions).map(o=>o.value);
}

// 1. 页面加载时全部 section 显示
// 设置生图提示词默认值和变量选择
window.onload = function() {
    document.getElementById('directionsSection').style.display = '';
    document.getElementById('imageGenSection').style.display = '';
    document.getElementById('videoGenSection').style.display = '';

    // 年龄
    const ageSel = document.getElementById('ageVarSelect');
    if (ageSel && ageSel.options.length === 0) {
        [
            {value:'young', label:'年轻 (Young)'},
            {value:'middle-aged', label:'中年 (Middle-aged)'},
            {value:'elderly', label:'老年 (Elderly)'}
        ].forEach(a => {
            const opt = document.createElement('option');
            opt.value = a.value;
            opt.innerText = a.label;
            ageSel.appendChild(opt);
        });
    }
    // 人种
    const raceSel = document.getElementById('raceVarSelect');
    if (raceSel && raceSel.options.length === 0) {
        [
            {value:'Caucasian', label:'Caucasian (欧洲裔白人)'},
            {value:'Mediterranean', label:'Mediterranean (西班牙/南欧特征)'},
            {value:'Latina', label:'Latina (墨西哥/拉丁美洲特征)'},
            {value:'African-American', label:'African-American (北美黑人)'},
            {value:'Afro-Latina', label:'Afro-Latina (拉丁美洲黑人)'},
            {value:'Mixed-race', label:'Mixed-race (European+Indigenous) (墨西哥混血特征)'}
        ].forEach(r => {
            const opt = document.createElement('option');
            opt.value = r.value;
            opt.innerText = r.label;
            raceSel.appendChild(opt);
        });
    }
    // 性别
    const genderSel = document.getElementById('genderVarSelect');
    if (genderSel && genderSel.options.length === 0) {
        [
            {value:'female', label:'女性 (Female)'},
            {value:'male', label:'男性 (Male)'}
        ].forEach(g => {
            const opt = document.createElement('option');
            opt.value = g.value;
            opt.innerText = g.label;
            genderSel.appendChild(opt);
        });
    }
    // 职业
    const occSel = document.getElementById('occupationVarSelect');
    if (occSel && occSel.options.length === 0) {
        [
            {value:'office worker', label:'上班族 (Office Worker)'},
            {value:'athlete', label:'运动员 (Athlete)'},
            {value:'student', label:'学生 (Student)'},
            {value:'doctor', label:'医生 (Doctor)'},
            {value:'teacher', label:'教师 (Teacher)'},
            {value:'retiree', label:'退休人士 (Retiree)'}
        ].forEach(o => {
            const opt = document.createElement('option');
            opt.value = o.value;
            opt.innerText = o.label;
            occSel.appendChild(opt);
        });
    }
    // 背景
    const bgSel = document.getElementById('bgVarSelect');
    if (bgSel && bgSel.options.length === 0) {
        [
            {value:'kitchen', label:'厨房 (Kitchen)'},
            {value:'living room', label:'客厅 (Living Room)'},
            {value:'bathroom', label:'浴室 (Bathroom)'},
            {value:'bedroom', label:'卧室 (Bedroom)'},
            {value:'outdoor', label:'户外 (Outdoor)'},
            {value:'studio', label:'摄影棚 (Studio)'}
        ].forEach(bg => {
            const opt = document.createElement('option');
            opt.value = bg.value;
            opt.innerText = bg.label;
            bgSel.appendChild(opt);
        });
    }
    // 人种选择
    if(!document.getElementById('raceVarSelect')) {
        const raceSel = document.createElement('select');
        raceSel.id = 'raceVarSelect';
        ['Caucasian','Latina','Asian'].forEach(r=>{
            const opt = document.createElement('option');
            opt.value = r;
            opt.innerText = r;
            raceSel.appendChild(opt);
        });
        const label = document.createElement('label');
        label.innerText = '人种:';
        label.htmlFor = 'raceVarSelect';
        label.style = 'margin-right:6px;';
        const container = document.getElementById('imageGenSection') || document.body;
        container.insertBefore(label, container.firstChild);
        container.insertBefore(raceSel, label.nextSibling);
    }
    // 默认prompt
    if(document.getElementById('promptEdit')) {
        document.getElementById('promptEdit').value =
`A high-resolution, upper-body portrait of a [[年龄变量]] [[人种变量]] [[性别变量]] [[职业描述]]. They are holding a small, single supplement bottle — approximately 5cm tall and 3cm in diameter — made of smooth, glossy, [[颜色变量]]-tinted transparent plastic. The capsule contents are clearly visible through the bottle. The product label and branding exactly match the reference image in shape, size, and detail. The bottle is held naturally in one hand — not a phone — with a relaxed grip. The gesture may vary: lifted slightly toward the camera, held beside the shoulder, resting by the side, or gesturing mid-sentence. The bottle appears proportionate and clearly visible, occupying part of the subject's palm without being oversized. Their facial expression is relaxed and candid — as if caught mid-sentence or in a natural pause. Skin texture is realistic with visible pores, no artificial gloss or smoothing. The camera simulates a front-facing phone camera positioned at shoulder or eye level, creating a natural vlog-style composition. Lighting is soft and diffused — coming from daylight or indoor ambient light. The background is fully visible and in sharp focus, matching a clean, realistic [[背景变量]] relevant to their lifestyle or profession. There is no artificial blur`;
    }
    // 1. 新增性别和背景变量选项UI
    if(!document.getElementById('genderVarSelect')) {
        const genderSel = document.createElement('select');
        genderSel.id = 'genderVarSelect';
        genderSel.multiple = true;
        genderSel.size = 2;
        genderSel.style = 'height:60px;width:90px;margin-right:10px;';
        [
            {value:'female', label:'女性 (Female)'},
            {value:'male', label:'男性 (Male)'},
        ].forEach(g=>{
            const opt = document.createElement('option');
            opt.value = g.value;
            opt.innerText = g.label;
            genderSel.appendChild(opt);
        });
        const label = document.createElement('label');
        label.innerText = '性别:';
        label.htmlFor = 'genderVarSelect';
        label.style = 'margin-right:6px;';
        const container = document.querySelector('.controls');
        container.insertBefore(label, container.children[2]);
        container.insertBefore(genderSel, label.nextSibling);
    }
    if(!document.getElementById('bgVarSelect')) {
        const bgSel = document.createElement('select');
        bgSel.id = 'bgVarSelect';
        bgSel.multiple = true;
        bgSel.size = 3;
        bgSel.style = 'height:60px;width:120px;margin-right:10px;';
        [
            {value:'kitchen', label:'厨房 (Kitchen)'},
            {value:'living room', label:'客厅 (Living Room)'},
            {value:'bathroom', label:'浴室 (Bathroom)'},
            {value:'bedroom', label:'卧室 (Bedroom)'},
            {value:'outdoor', label:'户外 (Outdoor)'},
            {value:'studio', label:'摄影棚 (Studio)'}
        ].forEach(bg=>{
            const opt = document.createElement('option');
            opt.value = bg.value;
            opt.innerText = bg.label;
            bgSel.appendChild(opt);
        });
        const label = document.createElement('label');
        label.innerText = '背景:';
        label.htmlFor = 'bgVarSelect';
        label.style = 'margin-right:6px;';
        const container = document.querySelector('.controls');
        container.insertBefore(label, container.children[4]);
        container.insertBefore(bgSel, label.nextSibling);
    }
    // 年龄变量选择 + 手动输入
    if(!document.getElementById('ageVarSelect')) {
        const ageSel = document.createElement('select');
        ageSel.id = 'ageVarSelect';
        ageSel.multiple = true;
        ageSel.size = 3;
        ageSel.style = 'height:60px;width:110px;margin-right:10px;';
        [
            {value:'young', label:'年轻 (Young)'},
            {value:'middle-aged', label:'中年 (Middle-aged)'},
            {value:'elderly', label:'老年 (Elderly)'}
        ].forEach(a=>{
            const opt = document.createElement('option');
            opt.value = a.value;
            opt.innerText = a.label;
            ageSel.appendChild(opt);
        });
        const label = document.createElement('label');
        label.innerText = '年龄:';
        label.htmlFor = 'ageVarSelect';
        label.style = 'margin-right:6px;';
        const input = document.createElement('input');
        input.type = 'text';
        input.id = 'ageVarManual';
        input.placeholder = '手动输入年龄';
        input.style = 'width:80px;margin-right:10px;';
        const container = document.querySelector('.controls');
        container.insertBefore(label, container.children[0]);
        container.insertBefore(ageSel, label.nextSibling);
        container.insertBefore(input, ageSel.nextSibling);
    }
    // 人种变量选择 + 手动输入
    if(!document.getElementById('raceVarSelect')) {
        const raceSel = document.createElement('select');
        raceSel.id = 'raceVarSelect';
        ['Caucasian','Latina','Asian'].forEach(r=>{
            const opt = document.createElement('option');
            opt.value = r;
            opt.innerText = r;
            raceSel.appendChild(opt);
        });
        const label = document.createElement('label');
        label.innerText = '人种:';
        label.htmlFor = 'raceVarSelect';
        label.style = 'margin-right:6px;';
        const input = document.createElement('input');
        input.type = 'text';
        input.id = 'raceVarManual';
        input.placeholder = '手动输入人种';
        input.style = 'width:80px;margin-right:10px;';
        const container = document.querySelector('.controls');
        container.insertBefore(label, container.children[2]);
        container.insertBefore(raceSel, label.nextSibling);
        container.insertBefore(input, raceSel.nextSibling);
    }
    // 性别变量选择 + 手动输入
    if(!document.getElementById('genderVarSelect')) {
        const genderSel = document.createElement('select');
        genderSel.id = 'genderVarSelect';
        genderSel.multiple = true;
        genderSel.size = 2;
        genderSel.style = 'height:60px;width:90px;margin-right:10px;';
        [
            {value:'female', label:'女性 (Female)'},
            {value:'male', label:'男性 (Male)'},
        ].forEach(g=>{
            const opt = document.createElement('option');
            opt.value = g.value;
            opt.innerText = g.label;
            genderSel.appendChild(opt);
        });
        const label = document.createElement('label');
        label.innerText = '性别:';
        label.htmlFor = 'genderVarSelect';
        label.style = 'margin-right:6px;';
        const input = document.createElement('input');
        input.type = 'text';
        input.id = 'genderVarManual';
        input.placeholder = '手动输入性别';
        input.style = 'width:60px;margin-right:10px;';
        const container = document.querySelector('.controls');
        container.insertBefore(label, container.children[4]);
        container.insertBefore(genderSel, label.nextSibling);
        container.insertBefore(input, genderSel.nextSibling);
    }
    // 职业描述选择 + 手动输入
    if(!document.getElementById('occupationVarSelect')) {
        const occSel = document.createElement('select');
        occSel.id = 'occupationVarSelect';
        occSel.multiple = true;
        occSel.size = 3;
        occSel.style = 'height:60px;width:130px;margin-right:10px;';
        [
            {value:'office worker', label:'上班族 (Office Worker)'},
            {value:'athlete', label:'运动员 (Athlete)'},
            {value:'student', label:'学生 (Student)'},
            {value:'doctor', label:'医生 (Doctor)'},
            {value:'teacher', label:'教师 (Teacher)'},
            {value:'retiree', label:'退休人士 (Retiree)'}
        ].forEach(o=>{
            const opt = document.createElement('option');
            opt.value = o.value;
            opt.innerText = o.label;
            occSel.appendChild(opt);
        });
        const label = document.createElement('label');
        label.innerText = '职业:';
        label.htmlFor = 'occupationVarSelect';
        label.style = 'margin-right:6px;';
        const input = document.createElement('input');
        input.type = 'text';
        input.id = 'occupationVarManual';
        input.placeholder = '手动输入职业';
        input.style = 'width:90px;margin-right:10px;';
        const container = document.querySelector('.controls');
        container.insertBefore(label, container.children[6]);
        container.insertBefore(occSel, label.nextSibling);
        container.insertBefore(input, occSel.nextSibling);
    }
};

// 批量生成图片
$('genImagesBtn').onclick = ()=>{
    $('genImagesBtn').disabled = true;
    $('genImagesBtn').innerText = '生成中...';
    
    // 清除旧的操作区域
    clearImageActions();
    
    let imageFile = '';
    let promptTpl = $('promptEdit') ? $('promptEdit').value.trim() : '';
    const races = getSelectedRaces();
    const genders = getSelectedGenders();
    const bgs = getSelectedBgs();
    const ages = getSelectedAges();
    const occupations = getSelectedOccupations();
    let color = 'orange';
    
    if(!selectedProduct || !selectedProduct.name) {
        alert('请先选择产品');
        $('genImagesBtn').disabled = false;
        $('genImagesBtn').innerText = '生成图片';
        return;
    }
    if(!promptTpl) {
        alert('请填写生图英文提示词');
        $('genImagesBtn').disabled = false;
        $('genImagesBtn').innerText = '生成图片';
        return;
    }
    if(races.length === 0) {
        alert('请至少选择一个人种');
        $('genImagesBtn').disabled = false;
        $('genImagesBtn').innerText = '生成图片';
        return;
    }
    if(genders.length === 0) {
        alert('请至少选择一个性别');
        $('genImagesBtn').disabled = false;
        $('genImagesBtn').innerText = '生成图片';
        return;
    }
    if(bgs.length === 0) {
        alert('请至少选择一个背景');
        $('genImagesBtn').disabled = false;
        $('genImagesBtn').innerText = '生成图片';
        return;
    }
    if(selectedProduct && selectedProduct.name && productColorMap[selectedProduct.name]) {
        color = productColorMap[selectedProduct.name];
    }
    
    let tasks = [];
    let done = 0;
    let failed = 0;
    let results = []; // 确保在这里定义 results
    
    function showResults() {
        $('imgList').innerHTML = results.filter(Boolean).join('');
        $('genImagesBtn').disabled = false;
        $('genImagesBtn').innerText = '生成图片';
    }
    
    function genForCombo(age, race, gender, occupation, bg, idx, results, showResults) {
        let ageEn = age; // already English
        let raceEn = raceVarMap[race] || race;
        let genderEn = genderVarMap[gender] || gender;
        let occupationEn = occupation; // already English
        let bgEn = bgVarMap[bg] || bg;
        let prompt = promptTpl
            .replace(/\[\[年龄变量\]\]/g, ageEn)
            .replace(/\[\[人种变量\]\]/g, raceEn)
            .replace(/\[\[性别变量\]\]/g, genderEn)
            .replace(/\[\[职业描述\]\]/g, occupationEn)
            .replace(/\[\[颜色变量\]\]/g, color)
            .replace(/\[\[背景变量\]\]/g, bgEn);
        
        // 调试：打印最终prompt
        console.log('最终prompt:', prompt);
        
        // 只用产品图片，不用 uploadedImageFile
        if(selectedProduct && selectedProduct.image_url) {
            imageFile = selectedProduct.image_url.replace('/product_image/', 'cp/');
            sendReq(imageFile);
        } else {
            failed++;
            results[idx] = `<div style='color:red;border:2px solid #cf1322;padding:8px;border-radius:8px;'>${raceEn}-${genderEn}-${bgEn} 未选择产品或图片</div>`;
            showResults();
        }
        
        function sendReq(imgFile){
            console.log('sendReq imageFile:', imgFile); // 调试
            fetch('/generate_image_task_test', {
                method:'POST',
                headers:{'Content-Type':'application/json'},
                body: JSON.stringify({
                    prompt: prompt,
                    image_file: imgFile,
                    seed: Math.floor(Math.random() * 1000000) + 1  // 随机种子
                })
            }).then(r=>r.json()).then(res=>{
                console.log('生成图片API响应:', res); // 调试
                if(res.task_id) {
                    pollImageTaskWebhookMulti(res.task_id, idx, `${raceEn}-${genderEn}-${bgEn}`, results, showResults);
                } else {
                    failed++;
                    console.log('生成图片失败，后端返回:', res); // 调试
                    results[idx] = `<div style='color:red;border:2px solid #cf1322;padding:8px;border-radius:8px;'>${raceEn}-${genderEn}-${bgEn} 生成失败: ${res.error || '未知错误'} <button class='btn' style='padding:2px 10px;font-size:13px;background:#fff0f0;color:#cf1322;border:1px solid #ffa39e;margin-left:8px;' onclick='retryGenImg(${JSON.stringify({race, gender, bg, idx})})'>重试</button></div>`;
                    showResults();
                }
            }).catch(err=>{
                failed++;
                console.log('生成图片网络/接口错误:', err); // 调试
                results[idx] = `<div style='color:red;border:2px solid #cf1322;padding:8px;border-radius:8px;'>${raceEn}-${genderEn}-${bgEn} 网络/接口错误: ${err.message||err} <button class='btn' style='padding:2px 10px;font-size:13px;background:#fff0f0;color:#cf1322;border:1px solid #ffa39e;margin-left:8px;' onclick='retryGenImg(${JSON.stringify({race, gender, bg, idx})})'>重试</button></div>`;
                showResults();
            });
        }
    }
    
    // 组合所有变量
    let combinations = [];
    ages.forEach(age => {
        races.forEach(race => {
            genders.forEach(gender => {
                occupations.forEach(occupation => {
                    bgs.forEach(bg => {
                        combinations.push({age, race, gender, occupation, bg});
                    });
                });
            });
        });
    });
    
    // 按顺序生成，避免过多并发请求
    combinations.forEach((combo, idx) => {
        setTimeout(() => {
            genForCombo(combo.age, combo.race, combo.gender, combo.occupation, combo.bg, idx, results, showResults);
        }, idx * 500); // 每个请求间隔500ms
    });
    
    // 提供重试函数到window
    window.retryGenImg = function({race, gender, bg, idx}) {
        genForCombo(race, gender, bg, idx, results, showResults);
    };
};

// 轮询定时器句柄
let imageTaskTimer = null;

function pollImageTask(task_id, prompt) {
    $('imgList').innerHTML = '图片生成中...';
    let timer = setInterval(()=>{
        fetch('/image_task_progress/' + task_id).then(r=>r.json()).then(res=>{
            if(res.status === 'completed' && res.image_urls) {
                clearInterval(timer);
            generatedImages = res.image_urls;
                lastPrompt = prompt;
            $('imgList').innerHTML = '';
            generatedImages.forEach((url,i)=>{
                const div = document.createElement('div');
                div.className = 'img-item';
                div.innerHTML = `<img src='${url}' alt='生成图片${i+1}'>`;
                div.onclick = ()=>{
                    selectImage(url, div, `生成图片${i+1}`);
                };
                $('imgList').appendChild(div);
            });
                // 移除所有 style.display = 'none' 或 style.display = '' 的切换逻辑
                // 在 getDirectionsBtn、direction-card、genImagesBtn 等点击事件中，不再控制 section 显示/隐藏
            $('videoGenSection').style.display = '';
            $('genVideoBtn').disabled = true;
                $('genImagesBtn').disabled = false;
            } else if(res.status === 'failed') {
                clearInterval(timer);
                $('imgList').innerHTML = '图片生成失败';
                $('genImagesBtn').disabled = false;
            }
        });
    }, 2500);
}

// 新增：本地图片上传并调用 /generate_image_task_test
$('uploadInput').onchange = function(){
    if(this.files && this.files[0]) {
        const file = this.files[0];
        const reader = new FileReader();
        reader.onload = function(e) {
            // 清除旧的操作区域
            clearImageActions();
            
            // 直接调用 /generate_image_task_test
            $('genImagesBtn').disabled = true;
            fetch('/generate_image_task_test', {
                method:'POST',
                headers:{'Content-Type':'application/json'},
                body: JSON.stringify({
                    prompt: lastPrompt || 'A beautiful product photo, studio lighting',
                    image_file: e.target.result,
                    seed: 1
                })
            }).then(r=>r.json()).then(res=>{
                if(res.task_id) {
                    pollImageTaskWebhook(res.task_id);
                } else {
                    alert('图片生成任务提交失败');
                    $('genImagesBtn').disabled = false;
                }
            });
        };
        reader.readAsDataURL(file);
    }
};

function pollImageTaskWebhook(task_id) {
    if(imageTaskTimer) clearInterval(imageTaskTimer);
    $('imgList').innerHTML = '图片生成中...';
    imageTaskTimer = setInterval(()=>{
        fetch('/image_task_progress/' + task_id).then(r=>r.json()).then(res=>{
            if(res.image_url) {
                clearInterval(imageTaskTimer);
                imageTaskTimer = null;
                
                // 创建图片项
                const div = document.createElement('div');
                div.className = 'img-item';
                div.innerHTML = `<img src='${res.image_url}' alt='生成图片'>`;
                div.onclick = ()=>{
                    selectImage(res.image_url, div, '生成图片');
                };
                
                $('imgList').innerHTML = '';
                $('imgList').appendChild(div);
                
                // 添加操作区域
                addImageActions(res.image_url, '生成图片', 0);
                
                $('genImagesBtn').disabled = false;
            } else if(res.image_urls && res.image_urls.length) {
                clearInterval(imageTaskTimer);
                imageTaskTimer = null;
                $('imgList').innerHTML = '';
                res.image_urls.forEach((url,i)=>{
                    const div = document.createElement('div');
                    div.className = 'img-item';
                    div.innerHTML = `<img src='${url}' alt='生成图片${i+1}'>`;
                    div.onclick = ()=>{
                        selectImage(url, div, `生成图片${i+1}`);
                    };
                    $('imgList').appendChild(div);
                });
                $('genImagesBtn').disabled = false;
            } else if(res.status === 'failed') {
                clearInterval(imageTaskTimer);
                imageTaskTimer = null;
                $('imgList').innerHTML = '图片生成失败';
                $('genImagesBtn').disabled = false;
            } else {
                $('imgList').innerHTML = '图片生成中...';
            }
        });
    }, 2500);
}

// 批量生成视频
function autoGenerateVideo(imageUrl, prompt) {
    if(!imageUrl) return;
    const videoCount = parseInt($('videoCountInput')?.value || 1);
    $('videoStatus').innerText = '正在生成视频，请稍候...';
    $('videoPreview').innerHTML = '';
    fetch('/generate_video_from_url', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({ image_url: imageUrl, prompt: prompt, video_count: videoCount })
    }).then(r=>r.json()).then(videoRes=>{
        if(videoRes.task_ids && Array.isArray(videoRes.task_ids)) {
            pollMultiVideoTasks(videoRes.task_ids);
        } else if(videoRes.task_id) {
            pollVideoTask(videoRes.task_id);
        } else {
            $('videoStatus').innerText = '视频生成任务提交失败';
        }
    });
}

// 批量轮询
function pollMultiVideoTasks(taskIds) {
    let completed = 0;
    let failed = 0;
    let results = [];
    $('videoStatus').innerText = `共${taskIds.length}个视频生成中...`;
    $('videoPreview').innerHTML = '';
    taskIds.forEach((task_id, idx)=>{
        let timer = setInterval(()=>{
            fetch('/video_task_progress/' + task_id).then(r=>r.json()).then(res=>{
                if(res.status === 'completed' && res.file_id) {
                    clearInterval(timer);
                    completed++;
                    const videoUrl = `/download_video/${task_id}`;
                    results[idx] = `<div style='margin-bottom:10px;'><b>视频${idx+1}：</b><br><video src='${videoUrl}' controls preload="auto" style='width:100%;max-width:400px;' type="video/mp4">您的浏览器不支持视频播放。</video><br><a href='${videoUrl}' download>下载视频</a></div>`;
                    $('videoPreview').innerHTML = results.filter(Boolean).join('');
                    $('videoStatus').innerText = `已完成${completed}个，失败${failed}个，共${taskIds.length}个`;
                } else if(res.status === 'failed') {
                    clearInterval(timer);
                    failed++;
                    results[idx] = `<div style='margin-bottom:10px;color:red;'>视频${idx+1}生成失败</div>`;
                    $('videoPreview').innerHTML = results.filter(Boolean).join('');
                    $('videoStatus').innerText = `已完成${completed}个，失败${failed}个，共${taskIds.length}个`;
                } else if(res.message) {
                    $('videoStatus').innerText = `视频${idx+1}：${res.message}（${completed+failed}/${taskIds.length}）`;
                }
            });
        }, 3000);
    });
}

function pollVideoTask(task_id) {
    $('videoStatus').innerText = '视频生成中...';
    let timer = setInterval(()=>{
        fetch('/video_task_progress/' + task_id).then(r=>r.json()).then(res=>{
            if(res.status === 'completed' && res.file_id) {
                clearInterval(timer);
                // 视频生成完成，显示预览和下载
                const videoUrl = `/download_video/${task_id}`;
                $('videoStatus').innerText = '视频生成完成';
                $('videoPreview').innerHTML = `<video src='${videoUrl}' controls preload="auto" style='width:100%;max-width:400px;' type="video/mp4">您的浏览器不支持视频播放。</video><br><a href='${videoUrl}' download>下载视频</a>`;
            } else if(res.status === 'failed') {
                clearInterval(timer);
                $('videoStatus').innerText = '视频生成失败';
            }
        });
    }, 3000);
}

// 独立视频生成与进度，记录任务id
window.autoGenerateVideoForImg = function(imageUrl, prompt, containerId, raceEn) {
    if(!imageUrl) return;
    const container = document.getElementById(containerId);
    if(!container) return;
    container.innerText = '正在生成视频，请稍候...';
    fetch('/generate_video_from_url', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({ image_url: imageUrl, prompt: prompt, video_count: 1 })
    }).then(r=>r.json()).then(videoRes=>{
        let taskId = '';
        if(videoRes.task_ids && Array.isArray(videoRes.task_ids)) {
            taskId = videoRes.task_ids[0];
            pollSingleVideoTask(taskId, container, raceEn, imageUrl);
        } else if(videoRes.task_id) {
            taskId = videoRes.task_id;
            pollSingleVideoTask(taskId, container, raceEn, imageUrl);
        } else {
            container.innerText = '视频生成任务提交失败';
        }
        // 记录任务id
        if(taskId) {
            let logDiv = document.getElementById('videoTaskLog');
            if(!logDiv) {
                logDiv = document.createElement('div');
                logDiv.id = 'videoTaskLog';
                logDiv.style = 'margin:16px 0;padding:10px;background:#f7f7f7;border-radius:6px;font-size:14px;color:#333;';
                document.body.appendChild(logDiv);
            }
            const info = `<div>人种: <b>${raceEn}</b> | 任务ID: <b>${taskId}</b> <br>图片: <span style='font-size:12px;'>${imageUrl}</span></div>`;
            logDiv.innerHTML += info;
        }
    });
}

// 优化轮询，避免刷屏
window.pollSingleVideoTask = function(task_id, container, raceEn, imageUrl) {
    let lastStatus = '';
    let lastMsg = '';
    container.innerText = '视频生成中...';
    let timer = setInterval(()=>{
        fetch('/video_task_progress/' + task_id).then(r=>r.json()).then(res=>{
            if(res.status === 'completed' && res.file_id) {
                clearInterval(timer);
                const videoUrl = `/download_video/${task_id}`;
                container.innerHTML = `<video src='${videoUrl}' controls preload="auto" style='width:180px;max-width:100%;' type="video/mp4">您的浏览器不支持视频播放。</video><br><a href='${videoUrl}' download>下载视频</a>`;
            } else if(res.status === 'failed') {
                clearInterval(timer);
                container.innerText = '视频生成失败';
            } else if(res.message) {
                // 只在状态变化时更新，避免刷屏
                if(res.status !== lastStatus || res.message !== lastMsg) {
                    container.innerText = res.message;
                    lastStatus = res.status;
                    lastMsg = res.message;
                }
            }
    });
    }, 3000);
}

// 4. 人种/直出模式切换自动刷新图片
// 限制人种多选最多3个
// 检查所有 getElementById(...).onchange = ... 赋值，确保元素已存在再赋值
if(document.getElementById('raceVarSelect')) {
    document.getElementById('raceVarSelect').onchange = function(e) {
        const sel = this;
        if(sel.selectedOptions.length > 3) {
            // 取消最后一次选择
            sel.options[sel.selectedIndex].selected = false;
            alert('最多只能选择3个人种');
            return;
        }
        if(selectedDirection) $('genImagesBtn').click();
    };
}
// 修正直出模式逻辑：勾选时不自动触发，仅点击生成图片按钮时才生图
$('directMode').onchange = function() {
    // 仅切换模式，不自动触发生图
};

// 5. 上传图片
// 5. 上传图片
$('uploadInput').onchange = function(){
    if(this.files && this.files[0]) {
        uploadedImageFile = this.files[0];
        selectedImageUrl = null;
        document.querySelectorAll('.img-item').forEach(e=>e.classList.remove('selected'));
        // 预览图片
        const reader = new FileReader();
        reader.onload = function(e) {
            // 显示图片预览和确认按钮
            let previewDiv = document.getElementById('uploadImgPreview');
            if (!previewDiv) {
                previewDiv = document.createElement('div');
                previewDiv.id = 'uploadImgPreview';
                previewDiv.style = 'margin:12px 0; text-align:center;';
                $('uploadInput').parentNode.parentNode.appendChild(previewDiv);
            }
            previewDiv.innerHTML = `<img src='${e.target.result}' alt='预览' style='max-width:220px;max-height:220px;border-radius:8px;box-shadow:0 2px 8px #0078ff22;'><br><button id='confirmGenVideoBtn' class='btn' style='margin-top:10px;'>确认生成视频</button>`;
            // 绑定确认按钮
            document.getElementById('confirmGenVideoBtn').onclick = function() {
                $('genVideoBtn').disabled = true;
                $('videoStatus').innerText = '正在上传图片并生成视频...';
                $('videoPreview').innerHTML = '';
                fetch('/generate_video_task', {
                    method:'POST',
                    headers:{'Content-Type':'application/json'},
                    body: JSON.stringify({image_path: e.target.result, prompt: lastPrompt})
                }).then(r=>r.json()).then(res=>{ handleVideoTaskNew(res); });
                this.disabled = true;
                this.innerText = '生成中...';
            };
        };
        reader.readAsDataURL(uploadedImageFile);
    }
};

// 6. 生成视频（新接口）
$('genVideoBtn').onclick = ()=>{
    $('genVideoBtn').disabled = true;
    $('videoStatus').innerText = '正在生成视频，请稍候...';
    $('videoPreview').innerHTML = '';
    let formData = new FormData();
    const duration = parseInt($('videoDurationSelectImg').value || '6');
    if(uploadedImageFile) {
        // 上传图片生成视频
        const reader = new FileReader();
        reader.onload = function(e) {
            fetch('/generate_video_task', {
                method:'POST',
                headers:{'Content-Type':'application/json'},
                body: JSON.stringify({image_path: e.target.result, prompt: lastPrompt, duration: duration})
            }).then(r=>r.json()).then(res=>{ handleVideoTaskNew(res); });
        };
        reader.readAsDataURL(uploadedImageFile);
        return;
    } else if(selectedImageUrl) {
        // 先下载图片到本地再生成视频
        fetch(selectedImageUrl).then(r=>r.blob()).then(blob=>{
            const reader = new FileReader();
            reader.onload = function(e) {
                fetch('/generate_video_task', {
                    method:'POST',
                    headers:{'Content-Type':'application/json'},
                    body: JSON.stringify({image_path: e.target.result, prompt: lastPrompt, duration: duration})
                }).then(r=>r.json()).then(res=>{ handleVideoTaskNew(res); });
            };
            reader.readAsDataURL(blob);
        });
        return;
    } else {
        alert('请先选择图片或上传图片');
        $('genVideoBtn').disabled = false;
        return;
    }
};

// 新增：纯文本视频生成功能
$('genVideoFromTextBtn').onclick = function() {
    const area = $('textVideoGenArea');
    if(area.style.display === 'none') {
        area.style.display = 'block';
        this.innerText = '隐藏文本生成';
    } else {
        area.style.display = 'none';
        this.innerText = '从文本生成视频';
    }
};

$('submitTextVideoBtn').onclick = function() {
    const prompt = $('videoPromptInput').value.trim();
    const model = $('videoModelSelect').value;
    const duration = parseInt($('videoDurationSelect').value);
    const resolution = $('videoResolutionSelect').value;
    
    if(!prompt) {
        alert('请输入视频生成提示词');
        return;
    }
    
    this.disabled = true;
    this.innerText = '生成中...';
    $('videoStatus').innerText = '正在提交视频生成任务...';
    $('videoPreview').innerHTML = '';
    
    fetch('/generate_video_from_text', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
            prompt: prompt,
            model: model,
            duration: duration,
            resolution: resolution
        })
    }).then(r => r.json()).then(res => {
        if(res.task_id) {
            addVideoTaskLog(res.task_id);
            pollVideoStatusNew(res.task_id);
            // 保存到历史记录
            saveHistoryRecord({
                video_id: res.task_id, 
                desc: `文本视频: ${prompt.substring(0, 30)}...`, 
                time: new Date().toLocaleString()
            });
        } else {
            $('videoStatus').innerText = '视频生成任务提交失败: ' + (res.error || '未知错误');
            $('submitTextVideoBtn').disabled = false;
            $('submitTextVideoBtn').innerText = '生成视频';
        }
    }).catch(err => {
        $('videoStatus').innerText = '网络错误: ' + err.message;
        $('submitTextVideoBtn').disabled = false;
        $('submitTextVideoBtn').innerText = '生成视频';
    });
};

function handleVideoTaskNew(res) {
    if(res.task_id) {
        addVideoTaskLog(res.task_id);
        pollVideoStatusNew(res.task_id);
    } else if(res.task_ids && Array.isArray(res.task_ids)) {
        res.task_ids.forEach((tid, idx)=>{
            addVideoTaskLog(tid, idx);
        });
        pollMultiVideoTasks(res.task_ids);
    } else {
        $('videoStatus').innerText = '视频生成失败';
        $('genVideoBtn').disabled = false;
    }
}

function pollVideoStatusNew(task_id) {
    console.log('开始轮询视频状态，任务ID:', task_id);
    $('videoStatus').innerText = '视频生成中...';
    let timer = setInterval(()=>{
        console.log('轮询视频状态:', task_id);
        fetch('/video_task_progress/' + task_id).then(r=>r.json()).then(res=>{
            console.log('视频状态响应:', res);
            if(res.status === 'completed' && res.file_id) {
                clearInterval(timer);
                console.log('视频生成完成');
                // 视频生成完成，显示预览和下载
                const videoUrl = `/download_video/${task_id}`;
                $('videoStatus').innerText = '视频生成完成';
                $('videoPreview').innerHTML = `
                    <div style="margin-bottom:12px;">
                        <video src='${videoUrl}' controls preload="auto" style='width:100%;max-width:500px;border-radius:8px;' type="video/mp4">
                            您的浏览器不支持视频播放。
                        </video>
                    </div>
                    <div style="display:flex;gap:8px;flex-wrap:wrap;">
                        <a href='${videoUrl}' download class='btn' style='padding:6px 16px;font-size:14px;'>下载视频</a>
                        <button class='copy-btn' onclick='navigator.clipboard.writeText("${task_id}");this.innerText="已复制";setTimeout(()=>this.innerText="复制任务ID",1000);'>复制任务ID</button>
                        <button class='btn' onclick='extendVideo("${task_id}")' style='background:#52c41a;padding:6px 16px;font-size:14px;'>延长生成</button>
                    </div>`;
                $('genVideoBtn').disabled = false;
                $('submitTextVideoBtn').disabled = false;
                $('submitTextVideoBtn').innerText = '生成视频';

                // 保存视频到历史记录
                saveVideoToHistory(task_id, videoUrl, res);
            } else if(res.status === 'failed') {
                clearInterval(timer);
                console.log('视频生成失败');
                $('videoStatus').innerText = '视频生成失败';
                $('genVideoBtn').disabled = false;
                $('submitTextVideoBtn').disabled = false;
                $('submitTextVideoBtn').innerText = '生成视频';
            } else if(res.message) {
                // 显示详细进度信息
                console.log('更新进度信息:', res.message);
                $('videoStatus').innerText = res.message;
            }
        }).catch(err => {
            console.error('轮询视频状态失败:', err);
            $('videoStatus').innerText = '查询状态失败，请稍后重试';
        });
    }, 3000);
}

// 任务ID展示区美化为可复制卡片
function addVideoTaskLog(taskId, idx) {
    let logDiv = document.getElementById('videoTaskLog');
    if(!logDiv) {
        logDiv = document.createElement('div');
        logDiv.id = 'videoTaskLog';
        logDiv.style = '';
        $('videoGenSection').appendChild(logDiv);
    }
    const card = document.createElement('div');
    card.className = 'video-task-card';
    card.innerHTML = `<span>视频任务${idx!==undefined?idx+1:''} ID: <span class='video-task-id'>${taskId}</span></span><button class='copy-btn' onclick='navigator.clipboard.writeText("${taskId}");this.innerText="已复制";setTimeout(()=>this.innerText="复制",1000);'>复制</button>`;
    logDiv.appendChild(card);
}

// 新增：视频预览功能
$('previewTaskIdBtn').onclick = function() {
    const tid = $('previewTaskIdInput').value.trim();
    if(!tid) return alert('请输入任务ID');
    
    // 先检查任务状态
    fetch('/check_video_status/' + tid).then(r => r.json()).then(res => {
        if(res.status === 'Finished' && res.file_id) {
            const videoUrl = `/download_video/${tid}`;
            $('customVideoPreview').innerHTML = `
                <div style="margin-bottom:12px;">
                    <video src='${videoUrl}' controls preload="auto" style='width:100%;max-width:480px;border-radius:8px;' type="video/mp4">
                        您的浏览器不支持视频播放。
                    </video>
                </div>
                <div style="display:flex;gap:8px;flex-wrap:wrap;">
                    <a href='${videoUrl}' download class='btn' style='padding:6px 16px;font-size:14px;'>下载视频</a>
                    <button class='copy-btn' onclick='navigator.clipboard.writeText("${tid}");this.innerText="已复制";setTimeout(()=>this.innerText="复制任务ID",1000);'>复制任务ID</button>
                </div>`;
        } else {
            $('customVideoPreview').innerHTML = `<div style="color:#cf1322;padding:8px;background:#fff1f0;border-radius:6px;">视频尚未完成，当前状态: ${res.status || '未知'}</div>`;
        }
    }).catch(err => {
        $('customVideoPreview').innerHTML = `<div style="color:#cf1322;padding:8px;background:#fff1f0;border-radius:6px;">查询失败: ${err.message}</div>`;
    });
};
$('localVideoInput').onchange = function() {
    if(this.files && this.files[0]) {
        const file = this.files[0];
        const url = URL.createObjectURL(file);
        $('customVideoPreview').innerHTML = `<video src='${url}' controls preload="auto" style='width:100%;max-width:480px;' type="video/mp4">您的浏览器不支持视频播放。</video>`;
    }
};

// 修改 pollImageTaskWebhookMulti 逻辑，增强错误处理和重试机制
function pollImageTaskWebhookMulti(task_id, idx, desc, results, showResults) {
    let fakeProgress = 0;
    let timer = null;
    let progressTimer = null;
    let retryCount = 0;
    const maxRetries = 3;
    
    function updateProgress() {
        if(fakeProgress < 95) {
            fakeProgress += 1;
            if(fakeProgress > 95) fakeProgress = 95;
            results[idx] = `
                <div class='img-item' style='position:relative;display:flex;flex-direction:column;align-items:center;padding:8px 0;'>
                  <div style='width:100%;margin-bottom:10px;'>
                    <div style='height:16px;background:#eaf4ff;border-radius:8px;overflow:hidden;'>
                      <div style='height:100%;width:${fakeProgress}%;background:#0078ff;transition:width 0.3s;'></div>
                    </div>
                    <div style='text-align:center;font-size:13px;color:#0078ff;margin-top:4px;'>${desc} 生成中... ${fakeProgress}%</div>
                  </div>
                </div>`;
            showResults();
        }
    }
    
    function startPolling() {
        progressTimer = setInterval(updateProgress, 600); // 1分钟到95%
        
        timer = setInterval(()=>{
            fetch('/image_task_progress/' + task_id)
            .then(r=>r.json())
            .then(res=>{
                console.log(`轮询图片任务 ${task_id} 进度:`, res);
                
                if(res.status === 'completed' && res.image_url) {
                    clearInterval(timer);
                    clearInterval(progressTimer);
                    
                    // 渲染图片
                    const imgUrl = res.image_url;
                    const card = `
                    <div class="img-item" onclick="selectImage('${imgUrl}', this, '${desc}')">
                      <img src='${imgUrl}' alt='${desc}' style="max-width:100%;height:auto;">
                    </div>`;
                    results[idx] = card;
                    showResults();
                    
                    // 在图片列表下方添加操作区域
                    addImageActions(imgUrl, desc, idx);
                    
                    // 保存到历史记录
                    saveHistoryRecord({
                        image_url: imgUrl, 
                        desc: `${desc} 图片`, 
                        time: new Date().toLocaleString()
                    });
                    
                } else if(res.status === 'failed') {
                    clearInterval(timer);
                    clearInterval(progressTimer);
                    
                    if(retryCount < maxRetries) {
                        retryCount++;
                        console.log(`图片生成失败，重试第 ${retryCount} 次...`);
                        results[idx] = `<div style='color:orange;border:2px solid #ffa500;padding:8px;border-radius:8px;'>${desc} 生成失败，正在重试 (${retryCount}/${maxRetries})...</div>`;
                        showResults();
                        
                        // 延迟后重试
                        setTimeout(() => {
                            startPolling();
                        }, 2000);
                    } else {
                        results[idx] = `<div style='color:red;border:2px solid #cf1322;padding:8px;border-radius:8px;'>${desc} 生成失败，已达到最大重试次数</div>`;
                        showResults();
                    }
                } else if(res.status === 'processing') {
                    // 继续轮询
                    console.log(`图片任务 ${task_id} 仍在处理中...`);
                }
            })
            .catch(err => {
                console.error(`轮询图片任务 ${task_id} 失败:`, err);
                if(retryCount < maxRetries) {
                    retryCount++;
                    console.log(`网络错误，重试第 ${retryCount} 次...`);
                    // 继续轮询，不清除定时器
                } else {
                    clearInterval(timer);
                    clearInterval(progressTimer);
                    results[idx] = `<div style='color:red;border:2px solid #cf1322;padding:8px;border-radius:8px;'>${desc} 网络错误，已达到最大重试次数</div>`;
                    showResults();
                }
            });
        }, 3000); // 每3秒查询一次
    }
    
    startPolling();
}

// 新增：清除图片操作区域
function clearImageActions() {
    const actionsContainer = document.getElementById('imageActionsContainer');
    if (actionsContainer) {
        actionsContainer.remove();
    }
}

// 新增：选择图片函数
function selectImage(imageUrl, element, desc) {
    // 移除所有选中状态
    document.querySelectorAll('.img-item').forEach(item => item.classList.remove('selected'));
    // 添加选中状态
    element.classList.add('selected');
    selectedImageUrl = imageUrl;
    uploadedImageFile = null;
    $('genVideoBtn').disabled = false;
    
    // 更新操作区域
    updateImageActions(imageUrl, desc);
}

// 新增：添加图片操作区域
function addImageActions(imageUrl, desc, idx) {
    let actionsContainer = document.getElementById('imageActionsContainer');
    if (!actionsContainer) {
        actionsContainer = document.createElement('div');
        actionsContainer.id = 'imageActionsContainer';
        actionsContainer.style = 'margin-top: 16px; padding: 16px; background: #f7f7f7; border-radius: 8px;';
        actionsContainer.innerHTML = '<div style="font-weight:600;color:#0078ff;margin-bottom:12px;">图片操作</div>';
        document.getElementById('imgList').parentNode.appendChild(actionsContainer);
    }
    
    // 添加操作按钮
    const actionHtml = `
        <div style="display:flex;gap:8px;align-items:center;flex-wrap:wrap;margin-bottom:8px;">
            <span style="font-size:14px;color:#666;">${desc}:</span>
            <a href='${imageUrl}' download class='btn' style='padding:6px 12px;font-size:13px;'>下载图片</a>
            <button class='btn' style='padding:6px 12px;font-size:13px;' onclick='showImgPreview("${imageUrl}")'>放大预览</button>
            <button class='btn' style='padding:6px 12px;font-size:13px;' onclick='startVideoGenWithProgress("${imageUrl}", "${lastPrompt.replace(/"/g, '\\"')}", "video-for-img-${idx}", "${desc}", this)' id='genVideoBtn-${idx}'>生成视频</button>
            <div id='video-for-img-${idx}' style='margin-left:8px;'></div>
        </div>
    `;
    actionsContainer.innerHTML += actionHtml;
}

// 新增：更新图片操作区域
function updateImageActions(imageUrl, desc) {
    let actionsContainer = document.getElementById('imageActionsContainer');
    if (actionsContainer) {
        actionsContainer.innerHTML = `
            <div style="font-weight:600;color:#0078ff;margin-bottom:12px;">图片操作</div>
            <div style="display:flex;gap:8px;align-items:center;flex-wrap:wrap;">
                <span style="font-size:14px;color:#666;">已选择: ${desc}</span>
                <a href='${imageUrl}' download class='btn' style='padding:6px 12px;font-size:13px;'>下载图片</a>
                <button class='btn' style='padding:6px 12px;font-size:13px;' onclick='showImgPreview("${imageUrl}")'>放大预览</button>
                <button class='btn' style='padding:6px 12px;font-size:13px;' onclick='generateVideoFromSelected()'>生成视频</button>
            </div>
        `;
    }
}

// 新增：从选中的图片生成视频
function generateVideoFromSelected() {
    if (!selectedImageUrl) {
        alert('请先选择一张图片');
        return;
    }
    
    $('genVideoBtn').click();
}

// 新增：生成视频带虚拟进度条（预计4分钟）
function startVideoGenWithProgress(imageUrl, prompt, containerId, desc, btnEl) {
    const container = document.getElementById(containerId);
    if(!container) return;
    if(btnEl) btnEl.disabled = true;
    let fakeProgress = 0;
    let totalSeconds = 240; // 4分钟
    let elapsed = 0;
    container.innerHTML = `<div style='width:100%;margin-bottom:10px;'>
        <div style='height:16px;background:#eaf4ff;border-radius:8px;overflow:hidden;'>
          <div id='video-progress-bar-${containerId}' style='height:100%;width:0%;background:#0078ff;transition:width 0.3s;'></div>
        </div>
        <div id='video-progress-text-${containerId}' style='text-align:center;font-size:13px;color:#0078ff;margin-top:4px;'>视频生成中... 0% (预计剩余4分0秒)</div>
      </div>`;
    let progressTimer = setInterval(()=>{
        if(fakeProgress < 97) {
            fakeProgress += 1;
            if(fakeProgress > 97) fakeProgress = 97;
            elapsed += 2.4; // 2400ms
            let remain = Math.max(0, totalSeconds - Math.round(elapsed));
            let min = Math.floor(remain / 60);
            let sec = remain % 60;
            document.getElementById('video-progress-bar-' + containerId).style.width = fakeProgress + '%';
            document.getElementById('video-progress-text-' + containerId).innerText =
                `视频生成中... ${fakeProgress}% (预计剩余${min}分${sec}秒)`;
        }
    }, 2400); // 4分钟到97%
    fetch('/generate_video_from_url', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({ image_url: imageUrl, prompt: prompt, video_count: 1 })
    }).then(r=>r.json()).then(videoRes=>{
        let taskId = '';
        if(videoRes.task_ids && Array.isArray(videoRes.task_ids)) {
            taskId = videoRes.task_ids[0];
        } else if(videoRes.task_id) {
            taskId = videoRes.task_id;
        } else {
            clearInterval(progressTimer);
            container.innerHTML = '视频生成任务提交失败';
            if(btnEl) btnEl.disabled = false;
            return;
        }
        // 轮询视频进度
        let pollTimer = setInterval(()=>{
            fetch('/video_task_progress/' + taskId).then(r=>r.json()).then(res=>{
                if(res.status === 'completed' && res.file_id) {
                    clearInterval(pollTimer);
                    clearInterval(progressTimer);
                    document.getElementById('video-progress-bar-' + containerId).style.width = '100%';
                    document.getElementById('video-progress-text-' + containerId).innerText = '视频生成完成 100%';
                    const videoUrl = `/download_video/${taskId}`;
                    setTimeout(()=>{
                        container.innerHTML = `
                            <video src='${videoUrl}' controls preload="auto" style='width:180px;max-width:100%;' type="video/mp4">您的浏览器不支持视频播放。</video><br>
                            <div style="display:flex;gap:4px;margin-top:8px;flex-wrap:wrap;">
                                <a href='${videoUrl}' download class='btn' style='font-size:12px;'>下载视频</a>
                                <button class='btn' onclick='extendVideo("${taskId}")' style='background:#52c41a;font-size:12px;'>延长生成</button>
                            </div>
                        `;
                        if(btnEl) btnEl.disabled = false;
                        // 保存视频到历史记录
                        saveVideoToHistory(taskId, videoUrl, res);
                    }, 500);
                } else if(res.status === 'failed') {
                    clearInterval(pollTimer);
                    clearInterval(progressTimer);
                    container.innerHTML = '视频生成失败';
                    if(btnEl) btnEl.disabled = false;
                } else if(res.message) {
                    // 优先显示后端 message
                    document.getElementById('video-progress-text-' + containerId).innerText =
                        `视频生成中... ${fakeProgress}% (${res.message})`;
                }
            });
        }, 3000);
    });
}
window.startVideoGenWithProgress = startVideoGenWithProgress;
// 图片放大预览弹窗
window.showImgPreview = function(url){
  let modal = document.getElementById('imgPreviewModal');
  if(!modal){
    modal = document.createElement('div');
    modal.id = 'imgPreviewModal';
    modal.style = 'position:fixed;left:0;top:0;width:100vw;height:100vh;background:rgba(0,0,0,0.7);z-index:9999;display:flex;align-items:center;justify-content:center;';
    modal.innerHTML = `<div style='position:relative;'><img id='imgPreviewBig' src='' style='max-width:90vw;max-height:90vh;border-radius:12px;box-shadow:0 4px 32px #0008;'><button id='closeImgPreview' style='position:absolute;top:-18px;right:-18px;background:#fff;color:#0078ff;border:none;border-radius:50%;width:36px;height:36px;font-size:22px;cursor:pointer;box-shadow:0 2px 8px #0003;'>×</button></div>`;
    document.body.appendChild(modal);
    document.getElementById('closeImgPreview').onclick = ()=>{modal.style.display='none';};
    modal.onclick = (e)=>{if(e.target===modal) modal.style.display='none';};
  }
  document.getElementById('imgPreviewBig').src = url;
  modal.style.display = 'flex';
}

// ====== 历史记录功能 ======
function saveHistoryRecord(record) {
    let history = JSON.parse(localStorage.getItem('genHistory')||'[]');
    history.unshift(record); // 新记录放前面
    if(history.length>50) history = history.slice(0,50); // 最多保留50条
    localStorage.setItem('genHistory', JSON.stringify(history));
    renderHistory();
}

// 保存视频到历史记录
function saveVideoToHistory(task_id, videoUrl, taskInfo) {
    // 获取任务详细信息以获取尾帧等数据
    fetch('/status/' + task_id).then(r=>r.json()).then(taskData=>{
        console.log('获取任务详细信息:', taskData);

        let historyRecord = {
            video_url: videoUrl,
            video_id: task_id,
            desc: `视频生成: ${taskData.prompt || '未知提示词'}`,
            time: new Date().toLocaleString(),
            type: 'video'
        };

        // 如果有尾帧图片，添加到记录中
        if(taskData.last_frame_url) {
            historyRecord.last_frame_url = taskData.last_frame_url;
            console.log('添加尾帧到历史记录:', taskData.last_frame_url);
        }

        saveHistoryRecord(historyRecord);
        console.log('视频已保存到历史记录');
    }).catch(err=>{
        console.error('获取任务详细信息失败:', err);
        // 即使获取详细信息失败，也保存基本的视频记录
        saveHistoryRecord({
            video_url: videoUrl,
            video_id: task_id,
            desc: '视频生成',
            time: new Date().toLocaleString(),
            type: 'video'
        });
    });
}
function renderHistory() {
    let history = JSON.parse(localStorage.getItem('genHistory')||'[]');
    if(!history.length) {
        $('historyList').innerHTML = '<div style="color:#aaa;">暂无历史记录</div>';
        return;
    }
    $('historyList').innerHTML = history.map((item,idx)=>{
        let thumb = '';
        let actions = '';

        // 处理图片记录
        if(item.image_url) {
            thumb = `<img src='${item.image_url}' class='history-thumb'>`;
            actions += `<button class='btn' onclick='previewHistoryImgModal("${item.image_url}")'>预览</button>`;
        }
        // 处理视频记录
        else if(item.video_url) {
            // 如果有尾帧图片，使用尾帧作为缩略图
            if(item.last_frame_url) {
                thumb = `<img src='${item.last_frame_url}' class='history-thumb' style='border: 2px solid #0078ff;'>`;
            } else {
                // 没有尾帧时显示视频图标
                thumb = `<div class='history-thumb' style='display:flex;align-items:center;justify-content:center;background:#f0f8ff;color:#0078ff;font-size:24px;'>🎬</div>`;
            }
            actions += `<button class='btn' onclick='previewHistoryVideoModal("${item.video_url}")'>播放</button>`;
            if(item.last_frame_url) {
                actions += `<button class='btn' onclick='previewHistoryImgModal("${item.last_frame_url}")'>尾帧</button>`;
                actions += `<button class='btn' onclick='generateFromLastFrame("${item.video_id}")'>续生成</button>`;
            }
        }

        return `<div class='history-card'>${thumb}<div class='history-info'><div class='history-meta'>${item.time||''}</div><div class='history-actions'>${actions}</div></div></div>`;
    }).join('');
}
// 预览大图弹窗函数
window.previewHistoryImgModal = function(url) {
  let modal = document.getElementById('historyImgPreviewModal');
  if(!modal){
    modal = document.createElement('div');
    modal.id = 'historyImgPreviewModal';
    modal.innerHTML = `<img id='historyImgPreviewBig' src='' alt='预览'><button id='closeHistoryImgPreview'>×</button>`;
    document.body.appendChild(modal);
    document.getElementById('closeHistoryImgPreview').onclick = ()=>{modal.style.display='none';};
    modal.onclick = (e)=>{if(e.target===modal) modal.style.display='none';};
  }
  document.getElementById('historyImgPreviewBig').src = url;
  modal.style.display = 'flex';
}

// 预览视频弹窗函数
window.previewHistoryVideoModal = function(url) {
  let modal = document.getElementById('historyVideoPreviewModal');
  if(!modal){
    modal = document.createElement('div');
    modal.id = 'historyVideoPreviewModal';
    modal.style.cssText = 'position:fixed;left:0;top:0;width:100vw;height:100vh;background:rgba(0,0,0,0.8);z-index:9999;display:flex;align-items:center;justify-content:center;display:none;';
    modal.innerHTML = `
      <div style="position:relative;max-width:90vw;max-height:90vh;">
        <video id='historyVideoPreviewPlayer' controls style="max-width:100%;max-height:100%;border-radius:12px;box-shadow:0 4px 32px #0008;">
          <source src="" type="video/mp4">
          您的浏览器不支持视频播放。
        </video>
        <button id='closeHistoryVideoPreview' style="position:absolute;top:-40px;right:0;background:#fff;color:#0078ff;border:none;border-radius:50%;width:36px;height:36px;font-size:22px;cursor:pointer;box-shadow:0 2px 8px #0003;">×</button>
      </div>
    `;
    document.body.appendChild(modal);
    document.getElementById('closeHistoryVideoPreview').onclick = ()=>{
      modal.style.display='none';
      document.getElementById('historyVideoPreviewPlayer').pause();
    };
    modal.onclick = (e)=>{
      if(e.target===modal) {
        modal.style.display='none';
        document.getElementById('historyVideoPreviewPlayer').pause();
      }
    };
  }
  document.getElementById('historyVideoPreviewPlayer').src = url;
  modal.style.display = 'flex';
}
function clearHistory() {
    if(confirm('确定要清空所有历史记录吗？')) {
        localStorage.removeItem('genHistory');
        renderHistory();
    }
}
// 页面加载时渲染历史
renderHistory();

// 页面卸载时清理定时器，防止无效轮询
window.onbeforeunload = function() {
    if (imageTaskTimer) clearInterval(imageTaskTimer);
};

// ====== 视频拼接功能 ======
let availableVideos = [];
let selectedVideos = [];

function loadAvailableVideos() {
    $('loadVideosBtn').disabled = true;
    $('loadVideosBtn').innerText = '加载中...';
    $('concatStatus').innerText = '正在加载可用视频...';

    fetch('/get_available_videos').then(r=>r.json()).then(data=>{
        if(data.videos) {
            availableVideos = data.videos;
            renderAvailableVideos();
            $('videoSelectionArea').style.display = 'block';
            $('concatStatus').innerText = `找到 ${data.count} 个可用视频`;
        } else {
            $('concatStatus').innerText = '获取视频列表失败';
        }
        $('loadVideosBtn').disabled = false;
        $('loadVideosBtn').innerText = '重新加载';
    }).catch(err=>{
        $('concatStatus').innerText = '加载失败: ' + err.message;
        $('loadVideosBtn').disabled = false;
        $('loadVideosBtn').innerText = '重新加载';
    });
}

function renderAvailableVideos() {
    const container = $('availableVideosList');
    container.innerHTML = availableVideos.map(video => {
        const isSelected = selectedVideos.some(v => v.id === video.id);
        const thumbnail = video.has_last_frame ? video.last_frame_url : '/static/video-placeholder.png';

        return `
            <div class="video-item ${isSelected ? 'selected' : ''}" onclick="toggleVideoSelection('${video.id}')">
                <img src="${thumbnail}" alt="视频缩略图" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <div style="display:none; width:100%; height:100px; background:#f0f0f0; border-radius:8px; display:flex; align-items:center; justify-content:center; color:#999; font-size:24px;">🎬</div>
                <div class="video-info">
                    <div style="font-weight:600;margin-bottom:4px;">${video.id}</div>
                    <div>大小: ${(video.size / 1024 / 1024).toFixed(1)}MB</div>
                    ${video.has_last_frame ? '<div style="color:#0078ff;">✓ 有尾帧</div>' : '<div style="color:#999;">无尾帧</div>'}
                    <div style="margin-top:6px;">
                        <button onclick="extractVideoLastFrameInline('${video.id}', this)" style="background:#52c41a;color:#fff;border:none;padding:2px 6px;border-radius:4px;font-size:10px;cursor:pointer;">
                            ${video.has_last_frame ? '重新提取' : '提取尾帧'}
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

function toggleVideoSelection(videoId) {
    const video = availableVideos.find(v => v.id === videoId);
    if (!video) return;

    const existingIndex = selectedVideos.findIndex(v => v.id === videoId);

    if (existingIndex >= 0) {
        // 取消选择
        selectedVideos.splice(existingIndex, 1);
    } else {
        // 添加选择
        selectedVideos.push(video);
    }

    renderAvailableVideos();
    renderSelectedVideos();
    updateConcatButton();
}

function renderSelectedVideos() {
    const container = $('selectedVideosList');
    $('selectedVideoCount').innerText = selectedVideos.length;

    container.innerHTML = selectedVideos.map((video, index) => {
        return `
            <div class="selected-video-tag">
                ${index + 1}. ${video.id}
                <button class="remove-btn" onclick="removeSelectedVideo(${index})">×</button>
            </div>
        `;
    }).join('');
}

function removeSelectedVideo(index) {
    selectedVideos.splice(index, 1);
    renderAvailableVideos();
    renderSelectedVideos();
    updateConcatButton();
}

function updateConcatButton() {
    const btn = $('seamlessConcatBtn');
    if (selectedVideos.length >= 2) {
        btn.disabled = false;
        btn.innerText = `拼接 ${selectedVideos.length} 个视频`;
    } else {
        btn.disabled = true;
        btn.innerText = '请选择至少2个视频';
    }
}

function performSeamlessConcat() {
    if (selectedVideos.length < 2) {
        alert('请至少选择2个视频进行拼接');
        return;
    }

    const btn = $('seamlessConcatBtn');
    btn.disabled = true;
    btn.innerText = '拼接中...';
    $('concatStatus').innerText = '正在进行无缝拼接，请稍候...';
    $('concatResult').innerHTML = '';

    const videoIds = selectedVideos.map(v => v.id);

    fetch('/seamless_concat', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({video_ids: videoIds})
    }).then(r=>r.json()).then(data=>{
        if(data.success) {
            $('concatStatus').innerText = '拼接成功！';
            $('concatResult').innerHTML = `
                <div style="background:#f0f8ff;padding:16px;border-radius:8px;border:1px solid #0078ff;">
                    <div style="font-weight:600;color:#0078ff;margin-bottom:12px;">✅ 无缝拼接完成</div>
                    <div style="margin-bottom:12px;">
                        <video src="${data.download_url}" controls preload="auto" style="width:100%;max-width:500px;border-radius:8px;">
                            您的浏览器不支持视频播放。
                        </video>
                    </div>
                    <div style="display:flex;gap:8px;">
                        <a href="${data.download_url}" download class="btn">下载拼接视频</a>
                        <button class="btn" onclick="copyToClipboard('${data.download_url}')">复制链接</button>
                    </div>
                </div>
            `;

            // 保存到历史记录
            saveHistoryRecord({
                video_url: data.download_url,
                video_id: data.output_video.replace('.mp4', ''),
                desc: `无缝拼接: ${selectedVideos.length}个视频`,
                time: new Date().toLocaleString(),
                type: 'seamless_concat'
            });

        } else {
            $('concatStatus').innerText = '拼接失败: ' + (data.error || '未知错误');
            $('concatResult').innerHTML = `
                <div style="background:#fff1f0;padding:16px;border-radius:8px;border:1px solid #cf1322;color:#cf1322;">
                    ❌ 拼接失败: ${data.error || '未知错误'}
                </div>
            `;
        }

        btn.disabled = false;
        btn.innerText = `拼接 ${selectedVideos.length} 个视频`;

    }).catch(err=>{
        $('concatStatus').innerText = '拼接失败: ' + err.message;
        $('concatResult').innerHTML = `
            <div style="background:#fff1f0;padding:16px;border-radius:8px;border:1px solid #cf1322;color:#cf1322;">
                ❌ 网络错误: ${err.message}
            </div>
        `;

        btn.disabled = false;
        btn.innerText = `拼接 ${selectedVideos.length} 个视频`;
    });
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(window.location.origin + text).then(()=>{
        alert('链接已复制到剪贴板');
    }).catch(()=>{
        alert('复制失败，请手动复制');
    });
}

// 内联尾帧提取功能（用于视频拼接界面）
function extractVideoLastFrameInline(videoId, btnElement) {
    const originalText = btnElement.innerText;
    btnElement.disabled = true;
    btnElement.innerText = '提取中...';

    fetch('/extract_last_frame', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({video_id: videoId})
    }).then(r=>r.json()).then(data=>{
        if(data.success) {
            btnElement.innerText = '重新提取';
            btnElement.style.background = '#389e0d';

            // 更新视频状态显示
            const videoInfo = btnElement.closest('.video-info');
            const statusDiv = videoInfo.querySelector('div:nth-child(3)');
            statusDiv.innerHTML = '<div style="color:#0078ff;">✓ 有尾帧</div>';

            // 显示成功提示
            showToast('✅ 尾帧提取成功', 'success');

            // 刷新可用视频列表
            setTimeout(() => {
                loadAvailableVideos();
            }, 1000);

        } else {
            btnElement.innerText = originalText;
            showToast('❌ 尾帧提取失败: ' + (data.error || '未知错误'), 'error');
        }

        btnElement.disabled = false;

    }).catch(err=>{
        btnElement.disabled = false;
        btnElement.innerText = originalText;
        showToast('❌ 网络错误: ' + err.message, 'error');
    });
}

// 简单的提示功能
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 16px;
        border-radius: 6px;
        color: #fff;
        font-size: 14px;
        z-index: 10000;
        max-width: 300px;
        word-wrap: break-word;
    `;

    if (type === 'success') {
        toast.style.background = '#52c41a';
    } else if (type === 'error') {
        toast.style.background = '#ff4d4f';
    } else {
        toast.style.background = '#1890ff';
    }

    toast.innerText = message;
    document.body.appendChild(toast);

    // 3秒后自动消失
    setTimeout(() => {
        if (toast.parentElement) {
            toast.parentElement.removeChild(toast);
        }
    }, 3000);
}

// ====== 尾帧提取功能 ======
let availableVideosForFrame = [];

function loadVideosForFrameExtraction() {
    $('loadVideosForFrameBtn').disabled = true;
    $('loadVideosForFrameBtn').innerText = '加载中...';
    $('frameExtractionStatus').innerText = '正在加载可用视频...';

    fetch('/get_available_videos').then(r=>r.json()).then(data=>{
        if(data.videos) {
            availableVideosForFrame = data.videos;
            renderVideosForFrameExtraction();
            $('frameExtractionArea').style.display = 'block';
            $('frameExtractionStatus').innerText = `找到 ${data.count} 个可用视频`;
        } else {
            $('frameExtractionStatus').innerText = '获取视频列表失败';
        }
        $('loadVideosForFrameBtn').disabled = false;
        $('loadVideosForFrameBtn').innerText = '重新加载';
    }).catch(err=>{
        $('frameExtractionStatus').innerText = '加载失败: ' + err.message;
        $('loadVideosForFrameBtn').disabled = false;
        $('loadVideosForFrameBtn').innerText = '重新加载';
    });
}

function renderVideosForFrameExtraction() {
    const container = $('videosForFrameList');
    container.innerHTML = availableVideosForFrame.map(video => {
        const thumbnail = video.has_last_frame ? video.last_frame_url : '/static/video-placeholder.png';
        const hasFrame = video.has_last_frame;

        return `
            <div class="frame-video-item">
                <img src="${thumbnail}" alt="视频缩略图" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <div style="display:none; width:100%; height:100px; background:#f0f0f0; border-radius:8px; display:flex; align-items:center; justify-content:center; color:#999; font-size:24px;">🎬</div>
                <div class="video-info">
                    <div style="font-weight:600;margin-bottom:4px;">${video.id}</div>
                    <div>大小: ${(video.size / 1024 / 1024).toFixed(1)}MB</div>
                    ${hasFrame ? '<div style="color:#52c41a;">✓ 已有尾帧</div>' : '<div style="color:#999;">无尾帧</div>'}
                </div>
                <button class="extract-btn" onclick="extractVideoLastFrame('${video.id}')" ${hasFrame ? '' : ''}>
                    ${hasFrame ? '重新提取' : '提取尾帧'}
                </button>
            </div>
        `;
    }).join('');
}

function extractVideoLastFrame(videoId) {
    const btn = event.target;
    const originalText = btn.innerText;
    btn.disabled = true;
    btn.innerText = '提取中...';

    // 清除之前的结果
    const existingResult = btn.parentElement.querySelector('.frame-result');
    if (existingResult) {
        existingResult.remove();
    }

    fetch('/extract_last_frame', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({video_id: videoId})
    }).then(r=>r.json()).then(data=>{
        if(data.success) {
            // 显示成功结果
            const resultDiv = document.createElement('div');
            resultDiv.className = 'frame-result';
            resultDiv.innerHTML = `
                <div style="font-weight:600;color:#52c41a;margin-bottom:8px;">✅ 尾帧提取成功</div>
                <div style="margin-bottom:8px;">
                    <img src="${data.last_frame_url}" alt="尾帧" style="width:100%;max-width:150px;border-radius:6px;">
                </div>
                <div style="display:flex;gap:4px;flex-wrap:wrap;">
                    <a href="${data.last_frame_url}" target="_blank" style="background:#52c41a;color:#fff;text-decoration:none;padding:4px 8px;border-radius:4px;font-size:12px;">查看大图</a>
                    <button onclick="generateFromLastFrame('${videoId}')" style="background:#1890ff;color:#fff;border:none;padding:4px 8px;border-radius:4px;font-size:12px;cursor:pointer;">续生成</button>
                </div>
            `;
            btn.parentElement.appendChild(resultDiv);

            // 更新按钮状态
            btn.innerText = '重新提取';

            // 更新视频信息显示
            const infoDiv = btn.parentElement.querySelector('.video-info');
            const statusDiv = infoDiv.querySelector('div:last-child');
            statusDiv.innerHTML = '<div style="color:#52c41a;">✓ 已有尾帧</div>';

            // 刷新可用视频列表（更新has_last_frame状态）
            setTimeout(() => {
                loadVideosForFrameExtraction();
            }, 1000);

        } else {
            // 显示错误结果
            const resultDiv = document.createElement('div');
            resultDiv.className = 'frame-result';
            resultDiv.style.background = '#fff2f0';
            resultDiv.style.borderColor = '#ffccc7';
            resultDiv.innerHTML = `
                <div style="font-weight:600;color:#cf1322;margin-bottom:4px;">❌ 提取失败</div>
                <div style="font-size:12px;color:#cf1322;">${data.error || '未知错误'}</div>
            `;
            btn.parentElement.appendChild(resultDiv);
            btn.innerText = originalText;
        }

        btn.disabled = false;

    }).catch(err=>{
        // 显示网络错误
        const resultDiv = document.createElement('div');
        resultDiv.className = 'frame-result';
        resultDiv.style.background = '#fff2f0';
        resultDiv.style.borderColor = '#ffccc7';
        resultDiv.innerHTML = `
            <div style="font-weight:600;color:#cf1322;margin-bottom:4px;">❌ 网络错误</div>
            <div style="font-size:12px;color:#cf1322;">${err.message}</div>
        `;
        btn.parentElement.appendChild(resultDiv);

        btn.disabled = false;
        btn.innerText = originalText;
    });
}

// ====== 从尾帧生成视频功能 ======
function generateFromLastFrame(videoId) {
    const prompt = prompt('请输入视频生成提示词（基于尾帧继续生成）:', '基于尾帧继续生成视频内容');
    if (!prompt || prompt.trim() === '') {
        return;
    }

    const duration = parseInt(prompt('请输入视频时长（秒）:', '6'));
    if (isNaN(duration) || duration < 1 || duration > 30) {
        alert('视频时长必须在1-30秒之间');
        return;
    }

    // 显示生成状态
    const statusDiv = document.createElement('div');
    statusDiv.id = `lastFrameGen_${videoId}`;
    statusDiv.style.cssText = 'position:fixed;top:20px;right:20px;background:#0078ff;color:#fff;padding:12px 16px;border-radius:8px;box-shadow:0 4px 12px #0078ff44;z-index:10000;';
    statusDiv.innerHTML = `
        <div style="font-weight:600;margin-bottom:4px;">🎬 从尾帧生成视频</div>
        <div style="font-size:12px;">视频ID: ${videoId}</div>
        <div style="font-size:12px;">状态: 提交中...</div>
    `;
    document.body.appendChild(statusDiv);

    fetch('/generate_from_last_frame', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
            video_id: videoId,
            prompt: prompt.trim(),
            duration: duration
        })
    }).then(r=>r.json()).then(data=>{
        if(data.task_id) {
            statusDiv.innerHTML = `
                <div style="font-weight:600;margin-bottom:4px;">🎬 从尾帧生成视频</div>
                <div style="font-size:12px;">视频ID: ${videoId}</div>
                <div style="font-size:12px;">状态: 生成中...</div>
                <div style="font-size:12px;">任务ID: ${data.task_id}</div>
            `;

            // 开始轮询任务状态
            pollLastFrameGenerationStatus(data.task_id, statusDiv);

        } else {
            statusDiv.innerHTML = `
                <div style="font-weight:600;margin-bottom:4px;">❌ 生成失败</div>
                <div style="font-size:12px;">${data.error || '未知错误'}</div>
                <button onclick="this.parentElement.remove()" style="margin-top:8px;background:#fff;color:#cf1322;border:none;padding:4px 8px;border-radius:4px;cursor:pointer;">关闭</button>
            `;
        }
    }).catch(err=>{
        statusDiv.innerHTML = `
            <div style="font-weight:600;margin-bottom:4px;">❌ 网络错误</div>
            <div style="font-size:12px;">${err.message}</div>
            <button onclick="this.parentElement.remove()" style="margin-top:8px;background:#fff;color:#cf1322;border:none;padding:4px 8px;border-radius:4px;cursor:pointer;">关闭</button>
        `;
    });
}

function pollLastFrameGenerationStatus(taskId, statusDiv) {
    const timer = setInterval(() => {
        fetch('/status/' + taskId).then(r=>r.json()).then(data=>{
            if(data.status === 'completed' && data.video_url) {
                clearInterval(timer);
                statusDiv.innerHTML = `
                    <div style="font-weight:600;margin-bottom:4px;">✅ 生成完成</div>
                    <div style="font-size:12px;">任务ID: ${taskId}</div>
                    <div style="margin-top:8px;">
                        <a href="${data.video_url}" target="_blank" style="background:#fff;color:#0078ff;text-decoration:none;padding:4px 8px;border-radius:4px;font-size:12px;margin-right:4px;">查看视频</a>
                        <button onclick="this.parentElement.parentElement.remove()" style="background:#fff;color:#666;border:none;padding:4px 8px;border-radius:4px;cursor:pointer;font-size:12px;">关闭</button>
                    </div>
                `;

                // 自动刷新历史记录
                renderHistory();

            } else if(data.status === 'failed') {
                clearInterval(timer);
                statusDiv.innerHTML = `
                    <div style="font-weight:600;margin-bottom:4px;">❌ 生成失败</div>
                    <div style="font-size:12px;">${data.message || '未知错误'}</div>
                    <button onclick="this.parentElement.remove()" style="margin-top:8px;background:#fff;color:#cf1322;border:none;padding:4px 8px;border-radius:4px;cursor:pointer;">关闭</button>
                `;
            } else {
                // 更新状态
                const statusText = data.status === 'processing' ? '生成中...' : data.status;
                statusDiv.querySelector('div:nth-child(3)').innerText = `状态: ${statusText}`;
            }
        }).catch(err=>{
            console.error('轮询状态失败:', err);
        });
    }, 3000);

    // 5分钟后停止轮询
    setTimeout(() => {
        clearInterval(timer);
        if(statusDiv.parentElement) {
            statusDiv.innerHTML += `<div style="font-size:12px;color:#ffcc00;margin-top:4px;">⚠️ 轮询超时，请手动刷新</div>`;
        }
    }, 300000);
}

// 新增：测试进度查询功能
$('testProgressBtn').onclick = function() {
    const tid = $('previewTaskIdInput').value.trim();
    if(!tid) return alert('请输入任务ID');
    
    console.log('手动测试进度查询:', tid);
    this.disabled = true;
    this.innerText = '查询中...';
    
    fetch('/video_task_progress/' + tid).then(r => r.json()).then(res => {
        console.log('进度查询结果:', res);
        this.disabled = false;
        this.innerText = '测试进度查询';
        
        let statusHtml = `<div style="padding:12px;background:#f7f7f7;border-radius:8px;margin-top:8px;">
            <div style="font-weight:600;color:#0078ff;margin-bottom:8px;">进度查询结果</div>
            <div><strong>任务ID:</strong> ${tid}</div>
            <div><strong>状态:</strong> ${res.status}</div>
            <div><strong>消息:</strong> ${res.message || '无'}</div>`;
        
        if(res.file_id) {
            statusHtml += `<div><strong>文件ID:</strong> ${res.file_id}</div>`;
        }
        if(res.download_url) {
            statusHtml += `<div><strong>下载链接:</strong> ${res.download_url}</div>`;
        }
        
        statusHtml += `</div>`;
        
        $('customVideoPreview').innerHTML = statusHtml;
        
        // 如果视频已完成，显示预览
        if(res.status === 'completed' && res.file_id) {
            const videoUrl = `/download_video/${tid}`;
            $('customVideoPreview').innerHTML += `
                <div style="margin-top:12px;">
                    <video src='${videoUrl}' controls preload="auto" style='width:100%;max-width:480px;border-radius:8px;' type="video/mp4">
                        您的浏览器不支持视频播放。
                    </video>
                    <div style="display:flex;gap:8px;flex-wrap:wrap;margin-top:8px;">
                        <a href='${videoUrl}' download class='btn' style='padding:6px 16px;font-size:14px;'>下载视频</a>
                        <button class='copy-btn' onclick='navigator.clipboard.writeText("${tid}");this.innerText="已复制";setTimeout(()=>this.innerText="复制任务ID",1000);'>复制任务ID</button>
                        <button class='btn' onclick='extendVideo("${tid}")' style='background:#52c41a;padding:6px 16px;font-size:14px;'>延长生成</button>
                    </div>

            // 保存视频到历史记录
            saveVideoToHistory(tid, videoUrl, res);
                </div>`;
        }
    }).catch(err => {
        console.error('进度查询失败:', err);
        this.disabled = false;
        this.innerText = '测试进度查询';
        $('customVideoPreview').innerHTML = `<div style="color:#cf1322;padding:8px;background:#fff1f0;border-radius:6px;">查询失败: ${err.message}</div>`;
    });
};

// ====== 变量保存与自动填充功能 ======
function showSaveVarTip(msg) {
    const tip = document.createElement('div');
    tip.innerText = msg;
    tip.style = 'position:fixed;top:60px;right:30px;background:#e6fffb;color:#08979c;border:1px solid #87e8de;border-radius:8px;padding:8px 16px;font-size:14px;z-index:1001;box-shadow:0 4px 12px rgba(0,0,0,0.08);';
    document.body.appendChild(tip);
    setTimeout(()=>{ tip.remove(); }, 1500);
}
function saveManualVar(key, inputId) {
    const val = document.getElementById(inputId)?.value.trim();
    if(val) {
        localStorage.setItem(key, val);
        showSaveVarTip('已保存: ' + val);
    } else {
        showSaveVarTip('请输入内容再保存');
    }
}
function loadManualVar(key, inputId) {
    const val = localStorage.getItem(key);
    if(val && document.getElementById(inputId)) {
        document.getElementById(inputId).value = val;
    }
}
document.addEventListener('DOMContentLoaded', function() {
    // 自动填充
    loadManualVar('ageVarManual', 'ageVarManual');
    loadManualVar('raceVarManual', 'raceVarManual');
    loadManualVar('genderVarManual', 'genderVarManual');
    loadManualVar('occupationVarManual', 'occupationVarManual');
    loadManualVar('bgVarManual', 'bgVarManual');
    // 绑定保存按钮
    document.getElementById('saveAgeVarBtn').onclick = ()=>saveManualVar('ageVarManual','ageVarManual');
    document.getElementById('saveRaceVarBtn').onclick = ()=>saveManualVar('raceVarManual','raceVarManual');
    document.getElementById('saveGenderVarBtn').onclick = ()=>saveManualVar('genderVarManual','genderVarManual');
    document.getElementById('saveOccupationVarBtn').onclick = ()=>saveManualVar('occupationVarManual','occupationVarManual');
    document.getElementById('saveBgVarBtn').onclick = ()=>saveManualVar('bgVarManual','bgVarManual');
});

// ====== 延长生成功能 ======
function extendVideo(videoId) {
    const prompt = window.prompt('请输入延长生成的提示词:', '继续生成视频内容');
    if (!prompt || prompt.trim() === '') {
        return;
    }

    const duration = parseInt(window.prompt('请输入延长视频时长（秒）:', '6'));
    if (isNaN(duration) || duration < 1 || duration > 30) {
        alert('视频时长必须在1-30秒之间');
        return;
    }

    // 创建延长生成状态显示
    const statusDiv = document.createElement('div');
    statusDiv.id = `extendStatus_${videoId}`;
    statusDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #fff;
        border: 2px solid #52c41a;
        border-radius: 12px;
        padding: 16px;
        box-shadow: 0 4px 16px rgba(82, 196, 26, 0.3);
        z-index: 10000;
        min-width: 300px;
        max-width: 400px;
    `;

    statusDiv.innerHTML = `
        <div style="font-weight:600;color:#52c41a;margin-bottom:12px;display:flex;align-items:center;">
            🎬 延长生成进行中
            <button onclick="this.parentElement.parentElement.remove()" style="margin-left:auto;background:none;border:none;font-size:18px;cursor:pointer;color:#999;">×</button>
        </div>
        <div style="font-size:12px;color:#666;margin-bottom:8px;">视频ID: ${videoId}</div>
        <div id="extendSteps_${videoId}">
            <div class="extend-step" data-step="extract_frame">
                <span class="step-icon">⏳</span> 提取尾帧
                <span class="step-status">等待中...</span>
            </div>
            <div class="extend-step" data-step="generate_video">
                <span class="step-icon">⏳</span> 生成新视频
                <span class="step-status">等待中...</span>
            </div>
            <div class="extend-step" data-step="seamless_concat">
                <span class="step-icon">⏳</span> 无缝拼接
                <span class="step-status">等待中...</span>
            </div>
        </div>
        <div id="extendResult_${videoId}" style="margin-top:12px;display:none;"></div>
    `;

    // 添加步骤样式
    if (!document.getElementById('extendStepStyles')) {
        const style = document.createElement('style');
        style.id = 'extendStepStyles';
        style.textContent = `
            .extend-step {
                display: flex;
                align-items: center;
                margin-bottom: 6px;
                font-size: 13px;
            }
            .extend-step .step-icon {
                margin-right: 8px;
                width: 16px;
            }
            .extend-step .step-status {
                margin-left: auto;
                font-size: 11px;
                color: #999;
            }
            .extend-step.processing .step-icon {
                animation: spin 1s linear infinite;
            }
            .extend-step.completed .step-icon {
                color: #52c41a;
            }
            .extend-step.failed .step-icon {
                color: #ff4d4f;
            }
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(statusDiv);

    // 提交延长生成请求
    fetch('/extend_video', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
            video_id: videoId,
            prompt: prompt.trim(),
            duration: duration
        })
    }).then(r=>r.json()).then(data=>{
        if(data.extend_task_id) {
            // 开始轮询延长状态
            pollExtendStatus(data.extend_task_id, videoId);
        } else {
            showExtendError(videoId, data.error || '延长生成启动失败');
        }
    }).catch(err=>{
        showExtendError(videoId, '网络错误: ' + err.message);
    });
}

function pollExtendStatus(extendTaskId, videoId) {
    const timer = setInterval(() => {
        fetch('/extend_status/' + extendTaskId).then(r=>r.json()).then(data=>{
            updateExtendSteps(videoId, data.steps || {});

            if(data.status === 'completed') {
                clearInterval(timer);
                showExtendSuccess(videoId, data);
            } else if(data.status === 'failed') {
                clearInterval(timer);
                showExtendError(videoId, data.message || '延长生成失败');
            }
        }).catch(err=>{
            console.error('轮询延长状态失败:', err);
        });
    }, 3000);

    // 10分钟后停止轮询
    setTimeout(() => {
        clearInterval(timer);
    }, 600000);
}

function updateExtendSteps(videoId, steps) {
    const stepsContainer = document.getElementById(`extendSteps_${videoId}`);
    if (!stepsContainer) return;

    Object.keys(steps).forEach(stepName => {
        const stepElement = stepsContainer.querySelector(`[data-step="${stepName}"]`);
        if (stepElement) {
            const step = steps[stepName];
            const icon = stepElement.querySelector('.step-icon');
            const status = stepElement.querySelector('.step-status');

            stepElement.className = `extend-step ${step.status}`;

            if (step.status === 'processing') {
                icon.textContent = '🔄';
                status.textContent = step.message || '处理中...';
            } else if (step.status === 'completed') {
                icon.textContent = '✅';
                status.textContent = step.message || '完成';
            } else if (step.status === 'failed') {
                icon.textContent = '❌';
                status.textContent = step.message || '失败';
            } else {
                icon.textContent = '⏳';
                status.textContent = step.message || '等待中...';
            }
        }
    });
}

function showExtendSuccess(videoId, data) {
    const resultDiv = document.getElementById(`extendResult_${videoId}`);
    if (resultDiv) {
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = `
            <div style="background:#f6ffed;border:1px solid #b7eb8f;border-radius:6px;padding:12px;">
                <div style="font-weight:600;color:#52c41a;margin-bottom:8px;">🎉 延长生成完成！</div>
                <div style="margin-bottom:8px;">
                    <video src="${data.final_video_url}" controls preload="auto" style="width:100%;max-width:250px;border-radius:6px;">
                        您的浏览器不支持视频播放。
                    </video>
                </div>
                <div style="display:flex;gap:6px;flex-wrap:wrap;">
                    <a href="${data.final_video_url}" download class="btn" style="font-size:12px;">下载视频</a>
                    <button onclick="extendVideo('${data.original_video_id}')" class="btn" style="background:#1890ff;font-size:12px;">再次延长</button>
                </div>
            </div>
        `;

        // 自动刷新历史记录
        setTimeout(() => {
            renderHistory();
        }, 1000);
    }
}

function showExtendError(videoId, errorMessage) {
    const resultDiv = document.getElementById(`extendResult_${videoId}`);
    if (resultDiv) {
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = `
            <div style="background:#fff2f0;border:1px solid #ffccc7;border-radius:6px;padding:12px;">
                <div style="font-weight:600;color:#cf1322;margin-bottom:4px;">❌ 延长生成失败</div>
                <div style="font-size:12px;color:#cf1322;">${errorMessage}</div>
            </div>
        `;
    }
}

// ====== 上传视频延长功能 ======
document.getElementById('uploadVideoExtendInput').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const btn = document.getElementById('uploadVideoExtendBtn');
    const preview = document.getElementById('uploadExtendPreview');

    if (file) {
        // 启用按钮
        btn.disabled = false;

        // 显示视频预览
        const videoUrl = URL.createObjectURL(file);
        preview.innerHTML = `
            <div style="background:#f7f7f7;border-radius:8px;padding:12px;">
                <div style="font-weight:600;margin-bottom:8px;">预览上传的视频：</div>
                <video src="${videoUrl}" controls preload="auto" style="width:100%;max-width:300px;border-radius:6px;">
                    您的浏览器不支持视频播放。
                </video>
                <div style="font-size:12px;color:#666;margin-top:4px;">
                    文件名: ${file.name} | 大小: ${(file.size / 1024 / 1024).toFixed(1)}MB
                </div>
            </div>
        `;
    } else {
        btn.disabled = true;
        preview.innerHTML = '';
    }
});

function uploadVideoExtend() {
    const fileInput = document.getElementById('uploadVideoExtendInput');
    const promptInput = document.getElementById('uploadExtendPrompt');
    const durationSelect = document.getElementById('uploadExtendDuration');
    const btn = document.getElementById('uploadVideoExtendBtn');

    if (!fileInput.files[0]) {
        alert('请选择视频文件');
        return;
    }

    const prompt = promptInput.value.trim();
    if (!prompt) {
        alert('请输入延长生成提示词');
        return;
    }

    const duration = parseInt(durationSelect.value);

    // 禁用按钮
    btn.disabled = true;
    btn.innerText = '上传中...';

    // 创建FormData
    const formData = new FormData();
    formData.append('video', fileInput.files[0]);
    formData.append('prompt', prompt);
    formData.append('duration', duration);

    // 创建上传延长状态显示
    const uploadId = 'upload_' + Date.now();
    const statusDiv = document.createElement('div');
    statusDiv.id = `uploadExtendStatus_${uploadId}`;
    statusDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #fff;
        border: 2px solid #fa8c16;
        border-radius: 12px;
        padding: 16px;
        box-shadow: 0 4px 16px rgba(250, 140, 22, 0.3);
        z-index: 10000;
        min-width: 300px;
        max-width: 400px;
    `;

    statusDiv.innerHTML = `
        <div style="font-weight:600;color:#fa8c16;margin-bottom:12px;display:flex;align-items:center;">
            📤 上传视频延长进行中
            <button onclick="this.parentElement.parentElement.remove()" style="margin-left:auto;background:none;border:none;font-size:18px;cursor:pointer;color:#999;">×</button>
        </div>
        <div style="font-size:12px;color:#666;margin-bottom:8px;">文件: ${fileInput.files[0].name}</div>
        <div id="uploadExtendSteps_${uploadId}">
            <div class="extend-step" data-step="extract_frame">
                <span class="step-icon">⏳</span> 提取尾帧
                <span class="step-status">等待中...</span>
            </div>
            <div class="extend-step" data-step="generate_video">
                <span class="step-icon">⏳</span> 生成新视频
                <span class="step-status">等待中...</span>
            </div>
            <div class="extend-step" data-step="seamless_concat">
                <span class="step-icon">⏳</span> 无缝拼接
                <span class="step-status">等待中...</span>
            </div>
        </div>
        <div id="uploadExtendResult_${uploadId}" style="margin-top:12px;display:none;"></div>
    `;

    document.body.appendChild(statusDiv);

    // 提交上传延长请求
    fetch('/upload_video_extend', {
        method: 'POST',
        body: formData
    }).then(r=>r.json()).then(data=>{
        btn.disabled = false;
        btn.innerText = '开始延长';

        if(data.extend_task_id) {
            // 显示上传成功信息
            const resultDiv = document.getElementById('uploadExtendResult');
            resultDiv.innerHTML = `
                <div style="background:#f6ffed;border:1px solid #b7eb8f;border-radius:6px;padding:12px;">
                    <div style="font-weight:600;color:#52c41a;margin-bottom:8px;">✅ 视频上传成功，延长任务已启动</div>
                    <div style="font-size:12px;color:#666;">任务ID: ${data.extend_task_id}</div>
                </div>
            `;

            // 开始轮询延长状态
            pollUploadExtendStatus(data.extend_task_id, uploadId);
        } else {
            showUploadExtendError(uploadId, data.error || '上传视频延长启动失败');
        }
    }).catch(err=>{
        btn.disabled = false;
        btn.innerText = '开始延长';
        showUploadExtendError(uploadId, '网络错误: ' + err.message);
    });
}

function pollUploadExtendStatus(extendTaskId, uploadId) {
    const timer = setInterval(() => {
        fetch('/extend_status/' + extendTaskId).then(r=>r.json()).then(data=>{
            updateUploadExtendSteps(uploadId, data.steps || {});

            if(data.status === 'completed') {
                clearInterval(timer);
                showUploadExtendSuccess(uploadId, data);
            } else if(data.status === 'failed') {
                clearInterval(timer);
                showUploadExtendError(uploadId, data.message || '上传视频延长失败');
            }
        }).catch(err=>{
            console.error('轮询上传延长状态失败:', err);
        });
    }, 3000);

    // 10分钟后停止轮询
    setTimeout(() => {
        clearInterval(timer);
    }, 600000);
}

function updateUploadExtendSteps(uploadId, steps) {
    const stepsContainer = document.getElementById(`uploadExtendSteps_${uploadId}`);
    if (!stepsContainer) return;

    Object.keys(steps).forEach(stepName => {
        const stepElement = stepsContainer.querySelector(`[data-step="${stepName}"]`);
        if (stepElement) {
            const step = steps[stepName];
            const icon = stepElement.querySelector('.step-icon');
            const status = stepElement.querySelector('.step-status');

            stepElement.className = `extend-step ${step.status}`;

            if (step.status === 'processing') {
                icon.textContent = '🔄';
                status.textContent = step.message || '处理中...';
            } else if (step.status === 'completed') {
                icon.textContent = '✅';
                status.textContent = step.message || '完成';
            } else if (step.status === 'failed') {
                icon.textContent = '❌';
                status.textContent = step.message || '失败';
            } else {
                icon.textContent = '⏳';
                status.textContent = step.message || '等待中...';
            }
        }
    });
}

function showUploadExtendSuccess(uploadId, data) {
    const resultDiv = document.getElementById(`uploadExtendResult_${uploadId}`);
    if (resultDiv) {
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = `
            <div style="background:#f6ffed;border:1px solid #b7eb8f;border-radius:6px;padding:12px;">
                <div style="font-weight:600;color:#52c41a;margin-bottom:8px;">🎉 上传视频延长完成！</div>
                <div style="margin-bottom:8px;">
                    <video src="${data.final_video_url}" controls preload="auto" style="width:100%;max-width:250px;border-radius:6px;">
                        您的浏览器不支持视频播放。
                    </video>
                </div>
                <div style="display:flex;gap:6px;flex-wrap:wrap;">
                    <a href="${data.final_video_url}" download class="btn" style="font-size:12px;">下载视频</a>
                    <a href="${data.uploaded_video_url || '#'}" download class="btn" style="background:#999;font-size:12px;">下载原视频</a>
                </div>
            </div>
        `;

        // 自动刷新历史记录
        setTimeout(() => {
            renderHistory();
        }, 1000);
    }
}

function showUploadExtendError(uploadId, errorMessage) {
    const resultDiv = document.getElementById(`uploadExtendResult_${uploadId}`);
    if (resultDiv) {
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = `
            <div style="background:#fff2f0;border:1px solid #ffccc7;border-radius:6px;padding:12px;">
                <div style="font-weight:600;color:#cf1322;margin-bottom:4px;">❌ 上传视频延长失败</div>
                <div style="font-size:12px;color:#cf1322;">${errorMessage}</div>
            </div>
        `;
    }
}
</script>
</body>
</html>