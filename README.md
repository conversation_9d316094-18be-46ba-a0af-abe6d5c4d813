# 产品展示视频生成应用

一个专业的产品展示视频生成工具，支持从图片生成高质量的产品介绍视频。

## 🎯 核心功能

- **智能图片生成**：基于产品模板快速生成专业产品图片
- **视频自动生成**：将产品图片转换为生动的展示视频
- **多时长支持**：支持6秒和10秒视频时长选择
- **专业提示词**：内置优化的视频生成提示词，确保视频质量

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置API密钥
编辑 `config.py` 文件，配置以下API密钥：
- MiniMax API Key（视频生成）
- RunningHub API Key（图片生成）

### 3. 启动应用
```bash
python image_to_video_app.py
```

### 4. 访问应用
打开浏览器访问：`http://localhost:5000`

## 📖 使用指南

### 图片生成
1. 选择产品模板（橙色/绿色/蓝色产品）
2. 可选：编辑生图提示词
3. 点击"生成图片"

### 视频生成
1. 选择时长（6秒或10秒）
2. 方式一：上传本地图片 → 点击"确认生成视频"
3. 方式二：选择已生成图片 → 点击"从图片生成视频"

## ⚙️ 配置说明

主要配置在 `config.py` 中：
- `API_KEY`: MiniMax视频生成API密钥
- `RUNNINGHUB_CONFIG`: 图片生成平台配置
- `VIDEO_CONFIG`: 视频生成参数

## 🛠️ 技术架构

- **后端**: Flask + Python
- **前端**: HTML5 + CSS3 + JavaScript
- **图片生成**: RunningHub API
- **视频生成**: MiniMax Hailuo API

## 📁 项目结构

```
├── image_to_video_app.py    # 主应用文件
├── config.py                # 配置文件
├── templates/
│   └── index.html          # 前端页面
├── static/                 # 静态资源
├── uploads/                # 上传文件目录
├── outputs/                # 输出文件目录
└── cp/                     # 产品模板图片
```

## 📝 注意事项

- 确保网络连接稳定（需要调用外部API）
- 视频生成需要时间，请耐心等待
- 建议定期清理临时文件

