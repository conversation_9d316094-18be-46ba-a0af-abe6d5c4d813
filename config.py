# 配置文件
# 请在此处配置你的MiniMax API Key

# MiniMax API Key - 请替换为你的实际API Key
API_KEY = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# DeepSeek API Key - 请替换为你的实际API Key
DEEPSEEK_API_KEY = "***********************************"
DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions"

# 应用配置
APP_CONFIG = {
    'UPLOAD_FOLDER': 'uploads',
    'OUTPUT_FOLDER': 'outputs',
    'MAX_CONTENT_LENGTH': 16 * 1024 * 1024,  # 16MB
    'ALLOWED_EXTENSIONS': {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
}

# 视频生成配置
VIDEO_CONFIG = {
    'model': 'MiniMax-Hailuo-02',
    'duration': 6,
    'resolution': '768P'
}

# 视频拼接配置
VIDEO_SEAMLESS_CONFIG = {
    'fps': 60,                          # 视频帧率
    'seam_duration_seconds': 0.5,       # 接缝处理区域的时长（秒）
    'rife_multiplication_factor': 2,    # RIFE AI插值的倍数
    'api_denoising_strength': 0.7,      # API生成时的连续性控制
    'ffmpeg_encode_preset': 'medium',   # FFmpeg编码预设
    'ffmpeg_crf_value': 23,             # FFmpeg视频质量参数
    'temp_dir': 'temp_video_processing', # 临时文件目录

    # RIFE算法优化参数
    'optical_flow_quality': 'high',     # 光流质量: 'low', 'medium', 'high'
    'interpolation_blend_ratio': 0.7,   # 光流插值与简单插值的混合比例
    'frame_resize_factor': 1.0,         # 处理时的帧缩放因子（提高速度）
    'use_gpu_acceleration': True,       # 是否使用GPU加速（如果可用）
    'max_frame_size': (1920, 1080),     # 最大处理帧尺寸
    'quality_vs_speed': 'balanced',     # 质量vs速度: 'speed', 'balanced', 'quality'
}

# API端点
API_ENDPOINTS = {
    'video_generation': 'https://api.minimaxi.com/v1/video_generation',
    'query_video': 'https://api.minimaxi.com/v1/query/video_generation',
    'file_retrieve': 'https://api.minimaxi.com/v1/files/retrieve'
}

# RunningHub配置
RUNNINGHUB_CONFIG = {
    'api_key': '8683e9e6f4714993b429e8a1d722d0b9',
    'base_url': 'https://www.runninghub.cn',
    'upload_endpoint': '/task/openapi/upload',
    'create_endpoint': '/task/openapi/create',
    'status_endpoint': '/task/openapi/status'
}

# 工作流ID配置
WORKFLOW_IDS = {
    'image_generation': '1942785263645208577',  # 图片生成工作流
    'dual_image_generation': '1942531246742507522',  # 双图生成工作流
    'single_image_generation': '1942766049638944770'  # 单图生成工作流
}

# ngrok 公网地址（每次启动 ngrok 后请手动更新）
NGROK_BASE_URL = "https://2f852257532f.ngrok-free.app"

# 安全配置
SECURITY_CONFIG = {
    'secret_key': 'your-secret-key-here',
    'csrf_protection': True,
    'rate_limiting': {
        'enabled': True,
        'requests_per_minute': 60
    }
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': 'app.log'
}

# 开发模式配置
DEBUG_CONFIG = {
    'debug': True,
    'testing': False,
    'verbose_logging': True
} 